{"name": "@moai/storybook", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "storybook dev -p 6006 --no-open", "build": "storybook build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"@moai/ui": "workspace:*"}, "devDependencies": {"@storybook/addon-controls": "8.6.12", "@storybook/addon-docs": "^8.6.12", "@storybook/addon-essentials": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/preview-api": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/theming": "^8.6.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.9", "eslint-plugin-storybook": "^0.8.0", "react": "^18.3.1", "react-dom": "^18.3.1", "storybook": "^8.6.12", "storybook-dark-mode": "4.0.2", "typescript": "^5.5.4", "vite": "^5.3.5"}}