.container {
  display: inline-flex;
  align-items: center;

  padding: 0 8px;
  font-weight: 500;

  /* Tag should have 24px height visually, but it should not be accounted
	in layout, as we want <PERSON> to act like an inline element (inline-flex to be
	exact) */
  height: 24px;
  margin: -4px 0; /* reset the height back to 16px */
}

:global(.light) .container {
  background-color: var(--category-1);
  color: var(--category-8);
}

:global(.dark) .container {
  background-color: var(--category-8);
  color: var(--category-1);
}

.smallTag {
  height: 24px;
  padding: 0 8px;
}

.mediumTag {
  height: 32px;
  padding: 0 12px;
}
