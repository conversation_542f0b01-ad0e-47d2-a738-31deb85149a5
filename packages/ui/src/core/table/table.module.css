/* Try to have as little CSS here as possible, so that our tables behave like
in native HTML */

/* container is the "table" tag */
.container {
  /* To make heading's bottom border visible while scrolling */
  border-collapse: separate;
  /* Avoid actually "separate" the borders */
  border-spacing: 0;
}
.containerFill {
  /* To make the table takes 100% of its container width */
  min-width: 100%;
}

/* Row's background change on hover */

/* Same as background.weak */
:global(.light) .container tr:hover td {
  background-color: var(--gray-0);
}
:global(.dark) .container tr:hover td {
  background-color: var(--gray-8);
}

/* Bordering */

.container th,
.container td {
  border-bottom-style: solid;
  border-bottom-width: 1px;
}

.container tr:last-child td {
  border-bottom-style: none;
}
