.top,
.left,
.right {
  position: sticky;
  z-index: var(--z-sticky-1);
}

.left {
  left: 0;
}

.right {
  right: 0;
}

.top {
  top: 0;
}

/* Fix z-index of the special top-left and top-right cell */
.top.left,
.top.right {
  z-index: var(--z-sticky-2);
}

/* Visual touch, don't affect sticky logic. The :not(.x) part is to win over
the default padding */
.leftLast:not(.x) {
  padding-right: 16px;
  border-right-width: 1px;
  border-right-style: solid;
}
.leftAdjacent:not(.x) {
  padding-left: 16px;
}
.rightLast:not(.x) {
  padding-left: 16px;
  border-left-width: 1px;
  border-left-style: solid;
}
.rightAdjacent:not(.x) {
  padding-right: 16px;
}
