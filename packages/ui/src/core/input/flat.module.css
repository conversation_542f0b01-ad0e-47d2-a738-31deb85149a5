.main {
  transition:
    background-color 0.1s,
    outline 0.2s ease-out;

  /* Pretty much nothing in normal state */
  text-align: inherit;
  text-decoration: inherit;

  /* Reset stuffs */
  border: none;
  border-radius: 0;
  background-color: none;
}

.main[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}

/* Mimic Button.styles.main hover */
:global(.light) .main:hover {
  background-color: var(--gray-1);
}
:global(.dark) .main:hover {
  background-color: var(--gray-6);
}

/* Mimic background.primary */
:global(.light) .main:focus {
  background-color: var(--white);
}
:global(.dark) .main:focus {
  background-color: var(--gray-9);
}

:global(.light) .main:disabled {
  color: var(--gray-3);
  /* https://stackoverflow.com/questions/262158/disabled-input-text-color */
  -webkit-text-fill-color: var(--gray-3);
  opacity: 1;
}
:global(.dark) .main:disabled {
  color: var(--gray-4);
  /* https://stackoverflow.com/questions/262158/disabled-input-text-color */
  -webkit-text-fill-color: var(--gray-4);
  opacity: 1;
}

/* PLACEHOLDER */

:global(.light) .main::placeholder {
  color: var(--gray-4);
  opacity: 1;
}

:global(.dark) .main::placeholder {
  color: var(--gray-3);
  opacity: 1;
}
