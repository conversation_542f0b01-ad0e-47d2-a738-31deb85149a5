.px1 {
  border-width: 1px;
  border-style: solid;
}

/* Color */

.weak {
  border-color: var(--border-weak);
}

.strong {
  border-color: var(--border-strong);
}

.borderFull {
  border-right: none;
  border-bottom: none;
  border-top-style: solid;
  border-left-style: solid;
  align-self: stretch;
  height: auto;
}

/* Radius */

.radius {
  border-radius: var(--radius-1);
}

:global(.group-tail) .radius {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

:global(.group-init) .radius {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
