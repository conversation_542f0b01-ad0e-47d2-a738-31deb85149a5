
export const Customization = (): JSX.Element => {
	const StatBlock = () => {
		return (
			<div style={{ padding: 16 }}>
				<Icon component={coreIcons.check} display="inline" />
				<strong style={{ marginLeft: 8 }} children="moai/moaijs" />
				<DivPx size={8} />
				<div
					style={{ opacity: 0.8 }}
					children="A React component library, where buttons look like buttons 🗿"
				/>
				<DivPx size={8} />
				<div style={{ display: "flex", alignItems: "center" }}>
					<div
						style={{
							marginRight: 8,
							width: 8,
							height: 8,
							backgroundColor: "#2b7489",
							borderRadius: 4,
						}}
					/>
					TypeScript
					<DivPx size={16} />
					moai.thien.do
				</div>
			</div>
		);
	};

	return (
		<div style={{ width: "fit-content" }}>
			<Popover
				placement="top-start"
				content={() => <StatBlock />}
				target={(popover) => (
					<Button
						onClick={() => {
							popover.toggle();
						}}
						children="moaijs/moai"
					/>
				)}
			/>
		</div>
	);
};
