import * as J from "react";
import le, { forwardRef as $i, useState as Pe, useEffect as et, useCallback as cn, Fragment as Va, cloneElement as Pi, useLayoutEffect as Ha, useRef as Di, createElement as Ri } from "react";
import * as kr from "react-dom";
import Ua, { createPortal as Ai } from "react-dom";
function qa(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
var mr = { exports: {} }, Zt = {};
/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Gr;
function Ya() {
  if (Gr) return Zt;
  Gr = 1;
  var e = le, t = Symbol.for("react.element"), n = Symbol.for("react.fragment"), r = Object.prototype.hasOwnProperty, i = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner, a = { key: !0, ref: !0, __self: !0, __source: !0 };
  function l(c, d, p) {
    var f, h = {}, b = null, g = null;
    p !== void 0 && (b = "" + p), d.key !== void 0 && (b = "" + d.key), d.ref !== void 0 && (g = d.ref);
    for (f in d) r.call(d, f) && !a.hasOwnProperty(f) && (h[f] = d[f]);
    if (c && c.defaultProps) for (f in d = c.defaultProps, d) h[f] === void 0 && (h[f] = d[f]);
    return { $$typeof: t, type: c, key: b, ref: g, props: h, _owner: i.current };
  }
  return Zt.Fragment = n, Zt.jsx = l, Zt.jsxs = l, Zt;
}
var Jt = {};
/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Kr;
function Ga() {
  return Kr || (Kr = 1, process.env.NODE_ENV !== "production" && function() {
    var e = le, t = Symbol.for("react.element"), n = Symbol.for("react.portal"), r = Symbol.for("react.fragment"), i = Symbol.for("react.strict_mode"), a = Symbol.for("react.profiler"), l = Symbol.for("react.provider"), c = Symbol.for("react.context"), d = Symbol.for("react.forward_ref"), p = Symbol.for("react.suspense"), f = Symbol.for("react.suspense_list"), h = Symbol.for("react.memo"), b = Symbol.for("react.lazy"), g = Symbol.for("react.offscreen"), j = Symbol.iterator, O = "@@iterator";
    function E(o) {
      if (o === null || typeof o != "object")
        return null;
      var v = j && o[j] || o[O];
      return typeof v == "function" ? v : null;
    }
    var N = e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    function T(o) {
      {
        for (var v = arguments.length, y = new Array(v > 1 ? v - 1 : 0), C = 1; C < v; C++)
          y[C - 1] = arguments[C];
        B("error", o, y);
      }
    }
    function B(o, v, y) {
      {
        var C = N.ReactDebugCurrentFrame, F = C.getStackAddendum();
        F !== "" && (v += "%s", y = y.concat([F]));
        var q = y.map(function(M) {
          return String(M);
        });
        q.unshift("Warning: " + v), Function.prototype.apply.call(console[o], console, q);
      }
    }
    var u = !1, R = !1, _ = !1, W = !1, G = !1, A;
    A = Symbol.for("react.module.reference");
    function L(o) {
      return !!(typeof o == "string" || typeof o == "function" || o === r || o === a || G || o === i || o === p || o === f || W || o === g || u || R || _ || typeof o == "object" && o !== null && (o.$$typeof === b || o.$$typeof === h || o.$$typeof === l || o.$$typeof === c || o.$$typeof === d || // This needs to include all possible module reference object
      // types supported by any Flight configuration anywhere since
      // we don't know which Flight build this will end up being used
      // with.
      o.$$typeof === A || o.getModuleId !== void 0));
    }
    function U(o, v, y) {
      var C = o.displayName;
      if (C)
        return C;
      var F = v.displayName || v.name || "";
      return F !== "" ? y + "(" + F + ")" : y;
    }
    function V(o) {
      return o.displayName || "Context";
    }
    function I(o) {
      if (o == null)
        return null;
      if (typeof o.tag == "number" && T("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), typeof o == "function")
        return o.displayName || o.name || null;
      if (typeof o == "string")
        return o;
      switch (o) {
        case r:
          return "Fragment";
        case n:
          return "Portal";
        case a:
          return "Profiler";
        case i:
          return "StrictMode";
        case p:
          return "Suspense";
        case f:
          return "SuspenseList";
      }
      if (typeof o == "object")
        switch (o.$$typeof) {
          case c:
            var v = o;
            return V(v) + ".Consumer";
          case l:
            var y = o;
            return V(y._context) + ".Provider";
          case d:
            return U(o, o.render, "ForwardRef");
          case h:
            var C = o.displayName || null;
            return C !== null ? C : I(o.type) || "Memo";
          case b: {
            var F = o, q = F._payload, M = F._init;
            try {
              return I(M(q));
            } catch {
              return null;
            }
          }
        }
      return null;
    }
    var P = Object.assign, S = 0, X, K, Y, ee, Z, Re, ve;
    function Ae() {
    }
    Ae.__reactDisabledLog = !0;
    function ke() {
      {
        if (S === 0) {
          X = console.log, K = console.info, Y = console.warn, ee = console.error, Z = console.group, Re = console.groupCollapsed, ve = console.groupEnd;
          var o = {
            configurable: !0,
            enumerable: !0,
            value: Ae,
            writable: !0
          };
          Object.defineProperties(console, {
            info: o,
            log: o,
            warn: o,
            error: o,
            group: o,
            groupCollapsed: o,
            groupEnd: o
          });
        }
        S++;
      }
    }
    function he() {
      {
        if (S--, S === 0) {
          var o = {
            configurable: !0,
            enumerable: !0,
            writable: !0
          };
          Object.defineProperties(console, {
            log: P({}, o, {
              value: X
            }),
            info: P({}, o, {
              value: K
            }),
            warn: P({}, o, {
              value: Y
            }),
            error: P({}, o, {
              value: ee
            }),
            group: P({}, o, {
              value: Z
            }),
            groupCollapsed: P({}, o, {
              value: Re
            }),
            groupEnd: P({}, o, {
              value: ve
            })
          });
        }
        S < 0 && T("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
      }
    }
    var ge = N.ReactCurrentDispatcher, Ie;
    function ue(o, v, y) {
      {
        if (Ie === void 0)
          try {
            throw Error();
          } catch (F) {
            var C = F.stack.trim().match(/\n( *(at )?)/);
            Ie = C && C[1] || "";
          }
        return `
` + Ie + o;
      }
    }
    var Me = !1, ye;
    {
      var at = typeof WeakMap == "function" ? WeakMap : Map;
      ye = new at();
    }
    function gt(o, v) {
      if (!o || Me)
        return "";
      {
        var y = ye.get(o);
        if (y !== void 0)
          return y;
      }
      var C;
      Me = !0;
      var F = Error.prepareStackTrace;
      Error.prepareStackTrace = void 0;
      var q;
      q = ge.current, ge.current = null, ke();
      try {
        if (v) {
          var M = function() {
            throw Error();
          };
          if (Object.defineProperty(M.prototype, "props", {
            set: function() {
              throw Error();
            }
          }), typeof Reflect == "object" && Reflect.construct) {
            try {
              Reflect.construct(M, []);
            } catch (de) {
              C = de;
            }
            Reflect.construct(o, [], M);
          } else {
            try {
              M.call();
            } catch (de) {
              C = de;
            }
            o.call(M.prototype);
          }
        } else {
          try {
            throw Error();
          } catch (de) {
            C = de;
          }
          o();
        }
      } catch (de) {
        if (de && C && typeof de.stack == "string") {
          for (var D = de.stack.split(`
`), oe = C.stack.split(`
`), Q = D.length - 1, te = oe.length - 1; Q >= 1 && te >= 0 && D[Q] !== oe[te]; )
            te--;
          for (; Q >= 1 && te >= 0; Q--, te--)
            if (D[Q] !== oe[te]) {
              if (Q !== 1 || te !== 1)
                do
                  if (Q--, te--, te < 0 || D[Q] !== oe[te]) {
                    var Ee = `
` + D[Q].replace(" at new ", " at ");
                    return o.displayName && Ee.includes("<anonymous>") && (Ee = Ee.replace("<anonymous>", o.displayName)), typeof o == "function" && ye.set(o, Ee), Ee;
                  }
                while (Q >= 1 && te >= 0);
              break;
            }
        }
      } finally {
        Me = !1, ge.current = q, he(), Error.prepareStackTrace = F;
      }
      var Wt = o ? o.displayName || o.name : "", Ot = Wt ? ue(Wt) : "";
      return typeof o == "function" && ye.set(o, Ot), Ot;
    }
    function yt(o, v, y) {
      return gt(o, !1);
    }
    function It(o) {
      var v = o.prototype;
      return !!(v && v.isReactComponent);
    }
    function qe(o, v, y) {
      if (o == null)
        return "";
      if (typeof o == "function")
        return gt(o, It(o));
      if (typeof o == "string")
        return ue(o);
      switch (o) {
        case p:
          return ue("Suspense");
        case f:
          return ue("SuspenseList");
      }
      if (typeof o == "object")
        switch (o.$$typeof) {
          case d:
            return yt(o.render);
          case h:
            return qe(o.type, v, y);
          case b: {
            var C = o, F = C._payload, q = C._init;
            try {
              return qe(q(F), v, y);
            } catch {
            }
          }
        }
      return "";
    }
    var Ne = Object.prototype.hasOwnProperty, ot = {}, bt = N.ReactDebugCurrentFrame;
    function Ye(o) {
      if (o) {
        var v = o._owner, y = qe(o.type, o._source, v ? v.type : null);
        bt.setExtraStackFrame(y);
      } else
        bt.setExtraStackFrame(null);
    }
    function $e(o, v, y, C, F) {
      {
        var q = Function.call.bind(Ne);
        for (var M in o)
          if (q(o, M)) {
            var D = void 0;
            try {
              if (typeof o[M] != "function") {
                var oe = Error((C || "React class") + ": " + y + " type `" + M + "` is invalid; it must be a function, usually from the `prop-types` package, but received `" + typeof o[M] + "`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");
                throw oe.name = "Invariant Violation", oe;
              }
              D = o[M](v, M, C, y, null, "SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");
            } catch (Q) {
              D = Q;
            }
            D && !(D instanceof Error) && (Ye(F), T("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).", C || "React class", y, M, typeof D), Ye(null)), D instanceof Error && !(D.message in ot) && (ot[D.message] = !0, Ye(F), T("Failed %s type: %s", y, D.message), Ye(null));
          }
      }
    }
    var Le = Array.isArray;
    function Ge(o) {
      return Le(o);
    }
    function Ke(o) {
      {
        var v = typeof Symbol == "function" && Symbol.toStringTag, y = v && o[Symbol.toStringTag] || o.constructor.name || "Object";
        return y;
      }
    }
    function _t(o) {
      try {
        return xt(o), !1;
      } catch {
        return !0;
      }
    }
    function xt(o) {
      return "" + o;
    }
    function wt(o) {
      if (_t(o))
        return T("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.", Ke(o)), xt(o);
    }
    var jt = N.ReactCurrentOwner, Mt = {
      key: !0,
      ref: !0,
      __self: !0,
      __source: !0
    }, bn, _n;
    function Gn(o) {
      if (Ne.call(o, "ref")) {
        var v = Object.getOwnPropertyDescriptor(o, "ref").get;
        if (v && v.isReactWarning)
          return !1;
      }
      return o.ref !== void 0;
    }
    function Kn(o) {
      if (Ne.call(o, "key")) {
        var v = Object.getOwnPropertyDescriptor(o, "key").get;
        if (v && v.isReactWarning)
          return !1;
      }
      return o.key !== void 0;
    }
    function Xn(o, v) {
      typeof o.ref == "string" && jt.current;
    }
    function m(o, v) {
      {
        var y = function() {
          bn || (bn = !0, T("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", v));
        };
        y.isReactWarning = !0, Object.defineProperty(o, "key", {
          get: y,
          configurable: !0
        });
      }
    }
    function x(o, v) {
      {
        var y = function() {
          _n || (_n = !0, T("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)", v));
        };
        y.isReactWarning = !0, Object.defineProperty(o, "ref", {
          get: y,
          configurable: !0
        });
      }
    }
    var w = function(o, v, y, C, F, q, M) {
      var D = {
        // This tag allows us to uniquely identify this as a React Element
        $$typeof: t,
        // Built-in properties that belong on the element
        type: o,
        key: v,
        ref: y,
        props: M,
        // Record the component responsible for creating this element.
        _owner: q
      };
      return D._store = {}, Object.defineProperty(D._store, "validated", {
        configurable: !1,
        enumerable: !1,
        writable: !0,
        value: !1
      }), Object.defineProperty(D, "_self", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: C
      }), Object.defineProperty(D, "_source", {
        configurable: !1,
        enumerable: !1,
        writable: !1,
        value: F
      }), Object.freeze && (Object.freeze(D.props), Object.freeze(D)), D;
    };
    function z(o, v, y, C, F) {
      {
        var q, M = {}, D = null, oe = null;
        y !== void 0 && (wt(y), D = "" + y), Kn(v) && (wt(v.key), D = "" + v.key), Gn(v) && (oe = v.ref, Xn(v, F));
        for (q in v)
          Ne.call(v, q) && !Mt.hasOwnProperty(q) && (M[q] = v[q]);
        if (o && o.defaultProps) {
          var Q = o.defaultProps;
          for (q in Q)
            M[q] === void 0 && (M[q] = Q[q]);
        }
        if (D || oe) {
          var te = typeof o == "function" ? o.displayName || o.name || "Unknown" : o;
          D && m(M, te), oe && x(M, te);
        }
        return w(o, D, oe, F, C, jt.current, M);
      }
    }
    var $ = N.ReactCurrentOwner, re = N.ReactDebugCurrentFrame;
    function ne(o) {
      if (o) {
        var v = o._owner, y = qe(o.type, o._source, v ? v.type : null);
        re.setExtraStackFrame(y);
      } else
        re.setExtraStackFrame(null);
    }
    var ze;
    ze = !1;
    function Lt(o) {
      return typeof o == "object" && o !== null && o.$$typeof === t;
    }
    function Fe() {
      {
        if ($.current) {
          var o = I($.current.type);
          if (o)
            return `

Check the render method of \`` + o + "`.";
        }
        return "";
      }
    }
    function Xt(o) {
      return "";
    }
    var Et = {};
    function st(o) {
      {
        var v = Fe();
        if (!v) {
          var y = typeof o == "string" ? o : o.displayName || o.name;
          y && (v = `

Check the top-level render call using <` + y + ">.");
        }
        return v;
      }
    }
    function xn(o, v) {
      {
        if (!o._store || o._store.validated || o.key != null)
          return;
        o._store.validated = !0;
        var y = st(v);
        if (Et[y])
          return;
        Et[y] = !0;
        var C = "";
        o && o._owner && o._owner !== $.current && (C = " It was passed a child from " + I(o._owner.type) + "."), ne(o), T('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.', y, C), ne(null);
      }
    }
    function zt(o, v) {
      {
        if (typeof o != "object")
          return;
        if (Ge(o))
          for (var y = 0; y < o.length; y++) {
            var C = o[y];
            Lt(C) && xn(C, v);
          }
        else if (Lt(o))
          o._store && (o._store.validated = !0);
        else if (o) {
          var F = E(o);
          if (typeof F == "function" && F !== o.entries)
            for (var q = F.call(o), M; !(M = q.next()).done; )
              Lt(M.value) && xn(M.value, v);
        }
      }
    }
    function Ft(o) {
      {
        var v = o.type;
        if (v == null || typeof v == "string")
          return;
        var y;
        if (typeof v == "function")
          y = v.propTypes;
        else if (typeof v == "object" && (v.$$typeof === d || // Note: Memo only checks outer props here.
        // Inner props are checked in the reconciler.
        v.$$typeof === h))
          y = v.propTypes;
        else
          return;
        if (y) {
          var C = I(v);
          $e(y, o.props, "prop", C, o);
        } else if (v.PropTypes !== void 0 && !ze) {
          ze = !0;
          var F = I(v);
          T("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?", F || "Unknown");
        }
        typeof v.getDefaultProps == "function" && !v.getDefaultProps.isReactClassApproved && T("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.");
      }
    }
    function Ia(o) {
      {
        for (var v = Object.keys(o.props), y = 0; y < v.length; y++) {
          var C = v[y];
          if (C !== "children" && C !== "key") {
            ne(o), T("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.", C), ne(null);
            break;
          }
        }
        o.ref !== null && (ne(o), T("Invalid attribute `ref` supplied to `React.Fragment`."), ne(null));
      }
    }
    var qr = {};
    function Yr(o, v, y, C, F, q) {
      {
        var M = L(o);
        if (!M) {
          var D = "";
          (o === void 0 || typeof o == "object" && o !== null && Object.keys(o).length === 0) && (D += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");
          var oe = Xt();
          oe ? D += oe : D += Fe();
          var Q;
          o === null ? Q = "null" : Ge(o) ? Q = "array" : o !== void 0 && o.$$typeof === t ? (Q = "<" + (I(o.type) || "Unknown") + " />", D = " Did you accidentally export a JSX literal instead of a component?") : Q = typeof o, T("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s", Q, D);
        }
        var te = z(o, v, y, F, q);
        if (te == null)
          return te;
        if (M) {
          var Ee = v.children;
          if (Ee !== void 0)
            if (C)
              if (Ge(Ee)) {
                for (var Wt = 0; Wt < Ee.length; Wt++)
                  zt(Ee[Wt], o);
                Object.freeze && Object.freeze(Ee);
              } else
                T("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
            else
              zt(Ee, o);
        }
        if (Ne.call(v, "key")) {
          var Ot = I(o), de = Object.keys(v).filter(function(Ba) {
            return Ba !== "key";
          }), Zn = de.length > 0 ? "{key: someKey, " + de.join(": ..., ") + ": ...}" : "{key: someKey}";
          if (!qr[Ot + Zn]) {
            var Wa = de.length > 0 ? "{" + de.join(": ..., ") + ": ...}" : "{}";
            T(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`, Zn, Ot, Wa, Ot), qr[Ot + Zn] = !0;
          }
        }
        return o === r ? Ia(te) : Ft(te), te;
      }
    }
    function Ma(o, v, y) {
      return Yr(o, v, y, !0);
    }
    function La(o, v, y) {
      return Yr(o, v, y, !1);
    }
    var za = La, Fa = Ma;
    Jt.Fragment = r, Jt.jsx = za, Jt.jsxs = Fa;
  }()), Jt;
}
process.env.NODE_ENV === "production" ? mr.exports = Ya() : mr.exports = Ga();
var s = mr.exports;
const Ka = "_strong_biexr_1", Xa = "_weak_biexr_6", pr = {
  strong: Ka,
  weak: Xa
}, _e = {
  strong: pr.strong,
  weak: pr.weak
}, Pp = ({ color: e, children: t }) => /* @__PURE__ */ s.jsx("div", { className: pr[e], children: t }), Za = "_px1_vljsi_1", Ja = "_weak_vljsi_8", Qa = "_strong_vljsi_12", eo = "_borderFull_vljsi_16", to = "_radius_vljsi_27", en = {
  px1: Za,
  weak: Ja,
  strong: Qa,
  borderFull: eo,
  radius: to
}, H = {
  // Radius
  radius: en.radius,
  // Size
  px1: en.px1,
  // Grayscale
  strong: en.strong,
  weak: en.weak
}, Ii = (e) => /* @__PURE__ */ s.jsx("hr", { className: [H.px1, en.borderFull, H[e.color]].join(" ") }), no = "_boxWeak_ipffw_1", ro = "_boxStrong_ipffw_5", io = "_dropWeak_ipffw_9", ao = "_dropStrong_ipffw_13", wn = {
  boxWeak: no,
  boxStrong: ro,
  dropWeak: io,
  dropStrong: ao
}, Gt = {
  boxWeak: wn.boxWeak,
  boxStrong: wn.boxStrong,
  dropWeak: wn.dropWeak,
  dropStrong: wn.dropStrong
}, oo = "_px_1l6zy_8", so = "_grow_1l6zy_13", Mi = {
  px: oo,
  grow: so
}, Dp = () => /* @__PURE__ */ s.jsx("span", { children: " " }), xe = ({ size: e }) => /* @__PURE__ */ s.jsx("span", { className: Mi.px, style: { width: e, height: e } }), Rp = () => /* @__PURE__ */ s.jsx("span", { className: Mi.grow }), co = "_block_9a5as_1", lo = "_inline_9a5as_5", Xr = {
  block: co,
  inline: lo
}, mt = (e) => {
  const t = e.size ?? 16;
  return e.component({
    // react-icons support "size" option but avoid that because we want to
    // support all generic components
    style: { width: t, height: t },
    className: e.display === "block" ? Xr.block : Xr.inline
  });
}, uo = "_normal_843yr_2", fo = "_always_843yr_3", Zr = {
  normal: uo,
  always: fo
}, Rt = {
  normal: Zr.normal,
  always: Zr.always
}, mo = "_wrapper_13hmu_1", po = "_container_13hmu_5", vo = "_track_13hmu_9", ho = "_head_13hmu_13", go = "_animate_13hmu_28", yo = "_neutral_13hmu_34", bo = "_highlight_13hmu_47", _o = "_inverse_13hmu_58", Be = {
  wrapper: mo,
  container: po,
  track: vo,
  head: ho,
  animate: go,
  neutral: yo,
  highlight: bo,
  inverse: _o
}, lt = 45, Jr = `M 50,50 m 0,-${lt} a ${lt},${lt} 0 1 1 0,${lt * 2} a ${lt},${lt} 0 1 1 0,-${lt * 2}`, rn = 280, xo = 4, wo = 16, jo = 100, Eo = (e) => {
  const t = lt + e / 2, n = (50 - t).toFixed(2), r = (t * 2).toFixed(2);
  return `${n} ${n} ${r} ${r}`;
}, Oo = (e) => {
  const t = Math.min(
    wo,
    xo * jo / e.size
  ), n = e.value === "indeterminate" ? 0.25 : e.value, r = rn - rn * n;
  return { width: t, offset: r };
}, tt = (e) => {
  const t = Oo(e), n = e.color ?? tt.colors.neutral;
  return /* @__PURE__ */ s.jsx("span", { className: Be.wrapper, children: /* @__PURE__ */ s.jsxs(
    "svg",
    {
      className: [
        Be.container,
        e.value === "indeterminate" ? Be.animate : "",
        n.container
      ].join(" "),
      width: e.size,
      height: e.size,
      strokeWidth: t.width,
      viewBox: Eo(t.width),
      children: [
        /* @__PURE__ */ s.jsx("path", { className: [Be.track, n.track].join(" "), d: Jr }),
        /* @__PURE__ */ s.jsx(
          "path",
          {
            className: [Be.head, n.head].join(" "),
            d: Jr,
            pathLength: rn,
            strokeDasharray: `${rn} ${rn}`,
            strokeDashoffset: t.offset
          }
        )
      ]
    }
  ) });
}, Jn = {
  head: Be.head,
  track: Be.track
};
tt.colors = {
  neutral: { ...Jn, container: Be.neutral },
  highlight: { ...Jn, container: Be.highlight },
  inverse: { ...Jn, container: Be.inverse }
};
const Co = "_button_ykilw_1", To = "_iconRight_ykilw_15", So = "_fill_ykilw_19", ko = "_text_ykilw_24", No = "_icon_ykilw_15", $o = "_busy_ykilw_33", Po = "_minWidth_ykilw_52", Tt = {
  button: Co,
  iconRight: To,
  fill: So,
  text: ko,
  icon: No,
  busy: $o,
  minWidth: Po
}, Do = "_flat_k73x5_3", Ro = "_outset_k73x5_13", jn = {
  flat: Do,
  outset: Ro
}, Ao = "_flat_uijut_3", Io = "_selected_uijut_21", Mo = "_outset_uijut_30", En = {
  flat: Ao,
  selected: Io,
  outset: Mo
}, Lo = "_flat_1bmwq_3", zo = "_selected_1bmwq_16", Fo = "_outset_1bmwq_25", On = {
  flat: Lo,
  selected: zo,
  outset: Fo
}, Wo = {
  outset: {
    selectedClassName: En.selected,
    mainClassName: En.outset,
    progressCircleColor: tt.colors.inverse
  },
  flat: {
    selectedClassName: En.selected,
    mainClassName: En.flat,
    progressCircleColor: tt.colors.highlight
  }
}, Bo = {
  outset: {
    mainClassName: jn.outset,
    selectedClassName: jn.selected,
    progressCircleColor: tt.colors.inverse
  },
  flat: {
    selectedClassName: jn.selected,
    mainClassName: jn.flat,
    progressCircleColor: tt.colors.highlight
  }
}, Vo = {
  outset: {
    mainClassName: On.outset,
    selectedClassName: On.selected,
    progressCircleColor: tt.colors.neutral
  },
  flat: {
    mainClassName: On.flat,
    selectedClassName: On.selected,
    progressCircleColor: tt.colors.neutral
  }
}, ln = { none: Vo, highlight: Wo, failure: Bo }, Li = (e) => e.highlight === !0 ? ln.highlight : e.color === void 0 ? ln.none : e.color, Ho = "_large_1b8fx_1", Uo = "_largeIcon_1b8fx_10", qo = "_medium_1b8fx_15", Yo = "_mediumIcon_1b8fx_21", Go = "_small_1b8fx_26", Ko = "_smallIcon_1b8fx_32", Kt = {
  large: Ho,
  largeIcon: Uo,
  medium: qo,
  mediumIcon: Yo,
  small: Go,
  smallIcon: Ko
}, zi = { iconSize: 20, iconMargin: 12 }, Fi = { iconSize: 16, iconMargin: 8 }, Wi = { iconSize: 12, iconMargin: 4 }, Xo = { mainClassName: Kt.large, ...zi }, Zo = { mainClassName: Kt.largeIcon, ...zi }, Jo = { mainClassName: Kt.medium, ...Fi }, Qo = { mainClassName: Kt.mediumIcon, ...Fi }, es = { mainClassName: Kt.small, ...Wi }, ts = { mainClassName: Kt.smallIcon, ...Wi }, Bi = {
  large: Xo,
  largeIcon: Zo,
  medium: Jo,
  mediumIcon: Qo,
  small: es,
  smallIcon: ts
}, ns = (e) => e.size ?? Bi.medium, rs = "_main_1sm0d_1", vr = {
  main: rs
}, is = "_main_1gv0x_1", as = "_busy_1gv0x_21", hr = {
  main: is,
  busy: as
}, os = {
  mainClassName: vr.main,
  busyClassName: vr.busy,
  color: (e) => e.flat
}, ss = {
  mainClassName: [H.radius, hr.main].join(" "),
  busyClassName: hr.busy,
  color: (e) => e.outset
}, Vi = { flat: os, outset: ss }, Hi = (e) => e.style ?? Vi.outset, cs = (e) => {
  const t = ns(e), n = Hi(e), r = n.color(Li(e)), i = [
    Tt.button,
    Rt.normal,
    t.mainClassName,
    n.mainClassName,
    r.mainClassName
  ];
  return e.busy && i.push(n.busyClassName), e.fill && i.push(Tt.fill), e.minWidth && i.push(Tt.minWidth), e.selected && i.push(r.selectedClassName), e.icon && e.iconRight && i.push(Tt.iconRight), i.join(" ");
}, ls = (e) => Hi(e).color(Li(e)).progressCircleColor, us = (e) => {
  const t = e.size ?? k.sizes.medium;
  return /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
    e.busy && /* @__PURE__ */ s.jsx("span", { className: Tt.busy, children: /* @__PURE__ */ s.jsx(
      tt,
      {
        size: t.iconSize,
        value: "indeterminate",
        color: ls(e)
      }
    ) }),
    e.icon && /* @__PURE__ */ s.jsx("span", { className: Tt.icon, children: /* @__PURE__ */ s.jsx(mt, { size: t.iconSize, component: e.icon, display: "block" }) }),
    e.icon && e.children && /* @__PURE__ */ s.jsx(xe, { size: t.iconMargin }),
    e.children && /* @__PURE__ */ s.jsx("span", { className: Tt.text, children: e.children })
  ] });
}, ds = (e) => e === k.sizes.largeIcon || e === k.sizes.mediumIcon || e === k.sizes.smallIcon, Qn = [
  [
    (e) => e.minWidth === !0 && ds(e.size),
    'Buttons that are icon-sized cannot have "minWidth" set'
  ],
  [
    (e) => e.icon === void 0 && e.children === void 0,
    'Button must have either "icon" or "children" defined so users can see it'
  ],
  [
    (e) => e.iconLabel === void 0 && e.children === void 0,
    'Button must have either "children" or "iconLabel" defined so screen reader can read it'
  ]
], fs = (e) => {
  for (let t = 0; t < Qn.length; t++)
    if (Qn[t][0](e)) throw Error(Qn[t][1]);
}, ms = (e, t) => {
  fs(e);
  const n = {
    id: e.id,
    // We need "any" because we can't type check the parameter type of
    // ref to yield error if an anchor ref is passed to a button :(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ref: t,
    className: cs(e),
    children: /* @__PURE__ */ s.jsx(us, { ...e }),
    "aria-label": e.iconLabel
  };
  return e.href ? /* @__PURE__ */ s.jsx(
    "a",
    {
      ...n,
      onClick: e.onClick,
      href: e.href,
      download: e.download,
      target: e.target,
      rel: e.rel || "noopener noreferrer"
    }
  ) : /* @__PURE__ */ s.jsx(
    "button",
    {
      ...n,
      onClick: e.onClick,
      onFocus: e.onFocus,
      onBlur: e.onBlur,
      disabled: e.disabled || e.busy,
      autoFocus: e.autoFocus,
      type: e.type ?? "button",
      tabIndex: e.dangerouslySetTabIndex
    }
  );
}, k = $i(ms);
k.styles = Vi;
k.colors = ln;
k.sizes = Bi;
const ps = "_action_y3jre_1", vs = {
  action: ps
}, Ui = ({ item: e }) => e === "divider" ? /* @__PURE__ */ s.jsxs("div", { children: [
  /* @__PURE__ */ s.jsx(xe, { size: 8 }),
  /* @__PURE__ */ s.jsx(Ii, { color: "weak" }),
  /* @__PURE__ */ s.jsx(xe, { size: 8 })
] }) : /* @__PURE__ */ s.jsx(
  "button",
  {
    onClick: () => {
      var t;
      return (t = e.fn) == null ? void 0 : t.call(e);
    },
    disabled: e.disabled,
    className: [
      vs.action,
      k.styles.flat.mainClassName,
      k.sizes.medium.mainClassName,
      k.styles.flat.color(k.colors.none).mainClassName
    ].join(" "),
    children: e.label
  }
);
var Qr = function(t) {
  return t.reduce(function(n, r) {
    var i = r[0], a = r[1];
    return n[i] = a, n;
  }, {});
}, ei = typeof window < "u" && window.document && window.document.createElement ? J.useLayoutEffect : J.useEffect, me = "top", Ce = "bottom", Te = "right", pe = "left", Nr = "auto", pn = [me, Ce, Te, pe], Ht = "start", un = "end", hs = "clippingParents", qi = "viewport", Qt = "popper", gs = "reference", ti = /* @__PURE__ */ pn.reduce(function(e, t) {
  return e.concat([t + "-" + Ht, t + "-" + un]);
}, []), Yi = /* @__PURE__ */ [].concat(pn, [Nr]).reduce(function(e, t) {
  return e.concat([t, t + "-" + Ht, t + "-" + un]);
}, []), ys = "beforeRead", bs = "read", _s = "afterRead", xs = "beforeMain", ws = "main", js = "afterMain", Es = "beforeWrite", Os = "write", Cs = "afterWrite", Ts = [ys, bs, _s, xs, ws, js, Es, Os, Cs];
function He(e) {
  return e ? (e.nodeName || "").toLowerCase() : null;
}
function we(e) {
  if (e == null)
    return window;
  if (e.toString() !== "[object Window]") {
    var t = e.ownerDocument;
    return t && t.defaultView || window;
  }
  return e;
}
function Pt(e) {
  var t = we(e).Element;
  return e instanceof t || e instanceof Element;
}
function Oe(e) {
  var t = we(e).HTMLElement;
  return e instanceof t || e instanceof HTMLElement;
}
function $r(e) {
  if (typeof ShadowRoot > "u")
    return !1;
  var t = we(e).ShadowRoot;
  return e instanceof t || e instanceof ShadowRoot;
}
function Ss(e) {
  var t = e.state;
  Object.keys(t.elements).forEach(function(n) {
    var r = t.styles[n] || {}, i = t.attributes[n] || {}, a = t.elements[n];
    !Oe(a) || !He(a) || (Object.assign(a.style, r), Object.keys(i).forEach(function(l) {
      var c = i[l];
      c === !1 ? a.removeAttribute(l) : a.setAttribute(l, c === !0 ? "" : c);
    }));
  });
}
function ks(e) {
  var t = e.state, n = {
    popper: {
      position: t.options.strategy,
      left: "0",
      top: "0",
      margin: "0"
    },
    arrow: {
      position: "absolute"
    },
    reference: {}
  };
  return Object.assign(t.elements.popper.style, n.popper), t.styles = n, t.elements.arrow && Object.assign(t.elements.arrow.style, n.arrow), function() {
    Object.keys(t.elements).forEach(function(r) {
      var i = t.elements[r], a = t.attributes[r] || {}, l = Object.keys(t.styles.hasOwnProperty(r) ? t.styles[r] : n[r]), c = l.reduce(function(d, p) {
        return d[p] = "", d;
      }, {});
      !Oe(i) || !He(i) || (Object.assign(i.style, c), Object.keys(a).forEach(function(d) {
        i.removeAttribute(d);
      }));
    });
  };
}
const Gi = {
  name: "applyStyles",
  enabled: !0,
  phase: "write",
  fn: Ss,
  effect: ks,
  requires: ["computeStyles"]
};
function Ve(e) {
  return e.split("-")[0];
}
var Nt = Math.max, Rn = Math.min, Ut = Math.round;
function gr() {
  var e = navigator.userAgentData;
  return e != null && e.brands && Array.isArray(e.brands) ? e.brands.map(function(t) {
    return t.brand + "/" + t.version;
  }).join(" ") : navigator.userAgent;
}
function Ki() {
  return !/^((?!chrome|android).)*safari/i.test(gr());
}
function qt(e, t, n) {
  t === void 0 && (t = !1), n === void 0 && (n = !1);
  var r = e.getBoundingClientRect(), i = 1, a = 1;
  t && Oe(e) && (i = e.offsetWidth > 0 && Ut(r.width) / e.offsetWidth || 1, a = e.offsetHeight > 0 && Ut(r.height) / e.offsetHeight || 1);
  var l = Pt(e) ? we(e) : window, c = l.visualViewport, d = !Ki() && n, p = (r.left + (d && c ? c.offsetLeft : 0)) / i, f = (r.top + (d && c ? c.offsetTop : 0)) / a, h = r.width / i, b = r.height / a;
  return {
    width: h,
    height: b,
    top: f,
    right: p + h,
    bottom: f + b,
    left: p,
    x: p,
    y: f
  };
}
function Pr(e) {
  var t = qt(e), n = e.offsetWidth, r = e.offsetHeight;
  return Math.abs(t.width - n) <= 1 && (n = t.width), Math.abs(t.height - r) <= 1 && (r = t.height), {
    x: e.offsetLeft,
    y: e.offsetTop,
    width: n,
    height: r
  };
}
function Xi(e, t) {
  var n = t.getRootNode && t.getRootNode();
  if (e.contains(t))
    return !0;
  if (n && $r(n)) {
    var r = t;
    do {
      if (r && e.isSameNode(r))
        return !0;
      r = r.parentNode || r.host;
    } while (r);
  }
  return !1;
}
function nt(e) {
  return we(e).getComputedStyle(e);
}
function Ns(e) {
  return ["table", "td", "th"].indexOf(He(e)) >= 0;
}
function vt(e) {
  return ((Pt(e) ? e.ownerDocument : (
    // $FlowFixMe[prop-missing]
    e.document
  )) || window.document).documentElement;
}
function Ln(e) {
  return He(e) === "html" ? e : (
    // this is a quicker (but less type safe) way to save quite some bytes from the bundle
    // $FlowFixMe[incompatible-return]
    // $FlowFixMe[prop-missing]
    e.assignedSlot || // step into the shadow DOM of the parent of a slotted node
    e.parentNode || // DOM Element detected
    ($r(e) ? e.host : null) || // ShadowRoot detected
    // $FlowFixMe[incompatible-call]: HTMLElement is a Node
    vt(e)
  );
}
function ni(e) {
  return !Oe(e) || // https://github.com/popperjs/popper-core/issues/837
  nt(e).position === "fixed" ? null : e.offsetParent;
}
function $s(e) {
  var t = /firefox/i.test(gr()), n = /Trident/i.test(gr());
  if (n && Oe(e)) {
    var r = nt(e);
    if (r.position === "fixed")
      return null;
  }
  var i = Ln(e);
  for ($r(i) && (i = i.host); Oe(i) && ["html", "body"].indexOf(He(i)) < 0; ) {
    var a = nt(i);
    if (a.transform !== "none" || a.perspective !== "none" || a.contain === "paint" || ["transform", "perspective"].indexOf(a.willChange) !== -1 || t && a.willChange === "filter" || t && a.filter && a.filter !== "none")
      return i;
    i = i.parentNode;
  }
  return null;
}
function vn(e) {
  for (var t = we(e), n = ni(e); n && Ns(n) && nt(n).position === "static"; )
    n = ni(n);
  return n && (He(n) === "html" || He(n) === "body" && nt(n).position === "static") ? t : n || $s(e) || t;
}
function Dr(e) {
  return ["top", "bottom"].indexOf(e) >= 0 ? "x" : "y";
}
function an(e, t, n) {
  return Nt(e, Rn(t, n));
}
function Ps(e, t, n) {
  var r = an(e, t, n);
  return r > n ? n : r;
}
function Zi() {
  return {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  };
}
function Ji(e) {
  return Object.assign({}, Zi(), e);
}
function Qi(e, t) {
  return t.reduce(function(n, r) {
    return n[r] = e, n;
  }, {});
}
var Ds = function(t, n) {
  return t = typeof t == "function" ? t(Object.assign({}, n.rects, {
    placement: n.placement
  })) : t, Ji(typeof t != "number" ? t : Qi(t, pn));
};
function Rs(e) {
  var t, n = e.state, r = e.name, i = e.options, a = n.elements.arrow, l = n.modifiersData.popperOffsets, c = Ve(n.placement), d = Dr(c), p = [pe, Te].indexOf(c) >= 0, f = p ? "height" : "width";
  if (!(!a || !l)) {
    var h = Ds(i.padding, n), b = Pr(a), g = d === "y" ? me : pe, j = d === "y" ? Ce : Te, O = n.rects.reference[f] + n.rects.reference[d] - l[d] - n.rects.popper[f], E = l[d] - n.rects.reference[d], N = vn(a), T = N ? d === "y" ? N.clientHeight || 0 : N.clientWidth || 0 : 0, B = O / 2 - E / 2, u = h[g], R = T - b[f] - h[j], _ = T / 2 - b[f] / 2 + B, W = an(u, _, R), G = d;
    n.modifiersData[r] = (t = {}, t[G] = W, t.centerOffset = W - _, t);
  }
}
function As(e) {
  var t = e.state, n = e.options, r = n.element, i = r === void 0 ? "[data-popper-arrow]" : r;
  i != null && (typeof i == "string" && (i = t.elements.popper.querySelector(i), !i) || Xi(t.elements.popper, i) && (t.elements.arrow = i));
}
const Is = {
  name: "arrow",
  enabled: !0,
  phase: "main",
  fn: Rs,
  effect: As,
  requires: ["popperOffsets"],
  requiresIfExists: ["preventOverflow"]
};
function Yt(e) {
  return e.split("-")[1];
}
var Ms = {
  top: "auto",
  right: "auto",
  bottom: "auto",
  left: "auto"
};
function Ls(e, t) {
  var n = e.x, r = e.y, i = t.devicePixelRatio || 1;
  return {
    x: Ut(n * i) / i || 0,
    y: Ut(r * i) / i || 0
  };
}
function ri(e) {
  var t, n = e.popper, r = e.popperRect, i = e.placement, a = e.variation, l = e.offsets, c = e.position, d = e.gpuAcceleration, p = e.adaptive, f = e.roundOffsets, h = e.isFixed, b = l.x, g = b === void 0 ? 0 : b, j = l.y, O = j === void 0 ? 0 : j, E = typeof f == "function" ? f({
    x: g,
    y: O
  }) : {
    x: g,
    y: O
  };
  g = E.x, O = E.y;
  var N = l.hasOwnProperty("x"), T = l.hasOwnProperty("y"), B = pe, u = me, R = window;
  if (p) {
    var _ = vn(n), W = "clientHeight", G = "clientWidth";
    if (_ === we(n) && (_ = vt(n), nt(_).position !== "static" && c === "absolute" && (W = "scrollHeight", G = "scrollWidth")), _ = _, i === me || (i === pe || i === Te) && a === un) {
      u = Ce;
      var A = h && _ === R && R.visualViewport ? R.visualViewport.height : (
        // $FlowFixMe[prop-missing]
        _[W]
      );
      O -= A - r.height, O *= d ? 1 : -1;
    }
    if (i === pe || (i === me || i === Ce) && a === un) {
      B = Te;
      var L = h && _ === R && R.visualViewport ? R.visualViewport.width : (
        // $FlowFixMe[prop-missing]
        _[G]
      );
      g -= L - r.width, g *= d ? 1 : -1;
    }
  }
  var U = Object.assign({
    position: c
  }, p && Ms), V = f === !0 ? Ls({
    x: g,
    y: O
  }, we(n)) : {
    x: g,
    y: O
  };
  if (g = V.x, O = V.y, d) {
    var I;
    return Object.assign({}, U, (I = {}, I[u] = T ? "0" : "", I[B] = N ? "0" : "", I.transform = (R.devicePixelRatio || 1) <= 1 ? "translate(" + g + "px, " + O + "px)" : "translate3d(" + g + "px, " + O + "px, 0)", I));
  }
  return Object.assign({}, U, (t = {}, t[u] = T ? O + "px" : "", t[B] = N ? g + "px" : "", t.transform = "", t));
}
function zs(e) {
  var t = e.state, n = e.options, r = n.gpuAcceleration, i = r === void 0 ? !0 : r, a = n.adaptive, l = a === void 0 ? !0 : a, c = n.roundOffsets, d = c === void 0 ? !0 : c, p = {
    placement: Ve(t.placement),
    variation: Yt(t.placement),
    popper: t.elements.popper,
    popperRect: t.rects.popper,
    gpuAcceleration: i,
    isFixed: t.options.strategy === "fixed"
  };
  t.modifiersData.popperOffsets != null && (t.styles.popper = Object.assign({}, t.styles.popper, ri(Object.assign({}, p, {
    offsets: t.modifiersData.popperOffsets,
    position: t.options.strategy,
    adaptive: l,
    roundOffsets: d
  })))), t.modifiersData.arrow != null && (t.styles.arrow = Object.assign({}, t.styles.arrow, ri(Object.assign({}, p, {
    offsets: t.modifiersData.arrow,
    position: "absolute",
    adaptive: !1,
    roundOffsets: d
  })))), t.attributes.popper = Object.assign({}, t.attributes.popper, {
    "data-popper-placement": t.placement
  });
}
const Fs = {
  name: "computeStyles",
  enabled: !0,
  phase: "beforeWrite",
  fn: zs,
  data: {}
};
var Cn = {
  passive: !0
};
function Ws(e) {
  var t = e.state, n = e.instance, r = e.options, i = r.scroll, a = i === void 0 ? !0 : i, l = r.resize, c = l === void 0 ? !0 : l, d = we(t.elements.popper), p = [].concat(t.scrollParents.reference, t.scrollParents.popper);
  return a && p.forEach(function(f) {
    f.addEventListener("scroll", n.update, Cn);
  }), c && d.addEventListener("resize", n.update, Cn), function() {
    a && p.forEach(function(f) {
      f.removeEventListener("scroll", n.update, Cn);
    }), c && d.removeEventListener("resize", n.update, Cn);
  };
}
const Bs = {
  name: "eventListeners",
  enabled: !0,
  phase: "write",
  fn: function() {
  },
  effect: Ws,
  data: {}
};
var Vs = {
  left: "right",
  right: "left",
  bottom: "top",
  top: "bottom"
};
function $n(e) {
  return e.replace(/left|right|bottom|top/g, function(t) {
    return Vs[t];
  });
}
var Hs = {
  start: "end",
  end: "start"
};
function ii(e) {
  return e.replace(/start|end/g, function(t) {
    return Hs[t];
  });
}
function Rr(e) {
  var t = we(e), n = t.pageXOffset, r = t.pageYOffset;
  return {
    scrollLeft: n,
    scrollTop: r
  };
}
function Ar(e) {
  return qt(vt(e)).left + Rr(e).scrollLeft;
}
function Us(e, t) {
  var n = we(e), r = vt(e), i = n.visualViewport, a = r.clientWidth, l = r.clientHeight, c = 0, d = 0;
  if (i) {
    a = i.width, l = i.height;
    var p = Ki();
    (p || !p && t === "fixed") && (c = i.offsetLeft, d = i.offsetTop);
  }
  return {
    width: a,
    height: l,
    x: c + Ar(e),
    y: d
  };
}
function qs(e) {
  var t, n = vt(e), r = Rr(e), i = (t = e.ownerDocument) == null ? void 0 : t.body, a = Nt(n.scrollWidth, n.clientWidth, i ? i.scrollWidth : 0, i ? i.clientWidth : 0), l = Nt(n.scrollHeight, n.clientHeight, i ? i.scrollHeight : 0, i ? i.clientHeight : 0), c = -r.scrollLeft + Ar(e), d = -r.scrollTop;
  return nt(i || n).direction === "rtl" && (c += Nt(n.clientWidth, i ? i.clientWidth : 0) - a), {
    width: a,
    height: l,
    x: c,
    y: d
  };
}
function Ir(e) {
  var t = nt(e), n = t.overflow, r = t.overflowX, i = t.overflowY;
  return /auto|scroll|overlay|hidden/.test(n + i + r);
}
function ea(e) {
  return ["html", "body", "#document"].indexOf(He(e)) >= 0 ? e.ownerDocument.body : Oe(e) && Ir(e) ? e : ea(Ln(e));
}
function on(e, t) {
  var n;
  t === void 0 && (t = []);
  var r = ea(e), i = r === ((n = e.ownerDocument) == null ? void 0 : n.body), a = we(r), l = i ? [a].concat(a.visualViewport || [], Ir(r) ? r : []) : r, c = t.concat(l);
  return i ? c : (
    // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here
    c.concat(on(Ln(l)))
  );
}
function yr(e) {
  return Object.assign({}, e, {
    left: e.x,
    top: e.y,
    right: e.x + e.width,
    bottom: e.y + e.height
  });
}
function Ys(e, t) {
  var n = qt(e, !1, t === "fixed");
  return n.top = n.top + e.clientTop, n.left = n.left + e.clientLeft, n.bottom = n.top + e.clientHeight, n.right = n.left + e.clientWidth, n.width = e.clientWidth, n.height = e.clientHeight, n.x = n.left, n.y = n.top, n;
}
function ai(e, t, n) {
  return t === qi ? yr(Us(e, n)) : Pt(t) ? Ys(t, n) : yr(qs(vt(e)));
}
function Gs(e) {
  var t = on(Ln(e)), n = ["absolute", "fixed"].indexOf(nt(e).position) >= 0, r = n && Oe(e) ? vn(e) : e;
  return Pt(r) ? t.filter(function(i) {
    return Pt(i) && Xi(i, r) && He(i) !== "body";
  }) : [];
}
function Ks(e, t, n, r) {
  var i = t === "clippingParents" ? Gs(e) : [].concat(t), a = [].concat(i, [n]), l = a[0], c = a.reduce(function(d, p) {
    var f = ai(e, p, r);
    return d.top = Nt(f.top, d.top), d.right = Rn(f.right, d.right), d.bottom = Rn(f.bottom, d.bottom), d.left = Nt(f.left, d.left), d;
  }, ai(e, l, r));
  return c.width = c.right - c.left, c.height = c.bottom - c.top, c.x = c.left, c.y = c.top, c;
}
function ta(e) {
  var t = e.reference, n = e.element, r = e.placement, i = r ? Ve(r) : null, a = r ? Yt(r) : null, l = t.x + t.width / 2 - n.width / 2, c = t.y + t.height / 2 - n.height / 2, d;
  switch (i) {
    case me:
      d = {
        x: l,
        y: t.y - n.height
      };
      break;
    case Ce:
      d = {
        x: l,
        y: t.y + t.height
      };
      break;
    case Te:
      d = {
        x: t.x + t.width,
        y: c
      };
      break;
    case pe:
      d = {
        x: t.x - n.width,
        y: c
      };
      break;
    default:
      d = {
        x: t.x,
        y: t.y
      };
  }
  var p = i ? Dr(i) : null;
  if (p != null) {
    var f = p === "y" ? "height" : "width";
    switch (a) {
      case Ht:
        d[p] = d[p] - (t[f] / 2 - n[f] / 2);
        break;
      case un:
        d[p] = d[p] + (t[f] / 2 - n[f] / 2);
        break;
    }
  }
  return d;
}
function dn(e, t) {
  t === void 0 && (t = {});
  var n = t, r = n.placement, i = r === void 0 ? e.placement : r, a = n.strategy, l = a === void 0 ? e.strategy : a, c = n.boundary, d = c === void 0 ? hs : c, p = n.rootBoundary, f = p === void 0 ? qi : p, h = n.elementContext, b = h === void 0 ? Qt : h, g = n.altBoundary, j = g === void 0 ? !1 : g, O = n.padding, E = O === void 0 ? 0 : O, N = Ji(typeof E != "number" ? E : Qi(E, pn)), T = b === Qt ? gs : Qt, B = e.rects.popper, u = e.elements[j ? T : b], R = Ks(Pt(u) ? u : u.contextElement || vt(e.elements.popper), d, f, l), _ = qt(e.elements.reference), W = ta({
    reference: _,
    element: B,
    placement: i
  }), G = yr(Object.assign({}, B, W)), A = b === Qt ? G : _, L = {
    top: R.top - A.top + N.top,
    bottom: A.bottom - R.bottom + N.bottom,
    left: R.left - A.left + N.left,
    right: A.right - R.right + N.right
  }, U = e.modifiersData.offset;
  if (b === Qt && U) {
    var V = U[i];
    Object.keys(L).forEach(function(I) {
      var P = [Te, Ce].indexOf(I) >= 0 ? 1 : -1, S = [me, Ce].indexOf(I) >= 0 ? "y" : "x";
      L[I] += V[S] * P;
    });
  }
  return L;
}
function Xs(e, t) {
  t === void 0 && (t = {});
  var n = t, r = n.placement, i = n.boundary, a = n.rootBoundary, l = n.padding, c = n.flipVariations, d = n.allowedAutoPlacements, p = d === void 0 ? Yi : d, f = Yt(r), h = f ? c ? ti : ti.filter(function(j) {
    return Yt(j) === f;
  }) : pn, b = h.filter(function(j) {
    return p.indexOf(j) >= 0;
  });
  b.length === 0 && (b = h);
  var g = b.reduce(function(j, O) {
    return j[O] = dn(e, {
      placement: O,
      boundary: i,
      rootBoundary: a,
      padding: l
    })[Ve(O)], j;
  }, {});
  return Object.keys(g).sort(function(j, O) {
    return g[j] - g[O];
  });
}
function Zs(e) {
  if (Ve(e) === Nr)
    return [];
  var t = $n(e);
  return [ii(e), t, ii(t)];
}
function Js(e) {
  var t = e.state, n = e.options, r = e.name;
  if (!t.modifiersData[r]._skip) {
    for (var i = n.mainAxis, a = i === void 0 ? !0 : i, l = n.altAxis, c = l === void 0 ? !0 : l, d = n.fallbackPlacements, p = n.padding, f = n.boundary, h = n.rootBoundary, b = n.altBoundary, g = n.flipVariations, j = g === void 0 ? !0 : g, O = n.allowedAutoPlacements, E = t.options.placement, N = Ve(E), T = N === E, B = d || (T || !j ? [$n(E)] : Zs(E)), u = [E].concat(B).reduce(function(ke, he) {
      return ke.concat(Ve(he) === Nr ? Xs(t, {
        placement: he,
        boundary: f,
        rootBoundary: h,
        padding: p,
        flipVariations: j,
        allowedAutoPlacements: O
      }) : he);
    }, []), R = t.rects.reference, _ = t.rects.popper, W = /* @__PURE__ */ new Map(), G = !0, A = u[0], L = 0; L < u.length; L++) {
      var U = u[L], V = Ve(U), I = Yt(U) === Ht, P = [me, Ce].indexOf(V) >= 0, S = P ? "width" : "height", X = dn(t, {
        placement: U,
        boundary: f,
        rootBoundary: h,
        altBoundary: b,
        padding: p
      }), K = P ? I ? Te : pe : I ? Ce : me;
      R[S] > _[S] && (K = $n(K));
      var Y = $n(K), ee = [];
      if (a && ee.push(X[V] <= 0), c && ee.push(X[K] <= 0, X[Y] <= 0), ee.every(function(ke) {
        return ke;
      })) {
        A = U, G = !1;
        break;
      }
      W.set(U, ee);
    }
    if (G)
      for (var Z = j ? 3 : 1, Re = function(he) {
        var ge = u.find(function(Ie) {
          var ue = W.get(Ie);
          if (ue)
            return ue.slice(0, he).every(function(Me) {
              return Me;
            });
        });
        if (ge)
          return A = ge, "break";
      }, ve = Z; ve > 0; ve--) {
        var Ae = Re(ve);
        if (Ae === "break") break;
      }
    t.placement !== A && (t.modifiersData[r]._skip = !0, t.placement = A, t.reset = !0);
  }
}
const Qs = {
  name: "flip",
  enabled: !0,
  phase: "main",
  fn: Js,
  requiresIfExists: ["offset"],
  data: {
    _skip: !1
  }
};
function oi(e, t, n) {
  return n === void 0 && (n = {
    x: 0,
    y: 0
  }), {
    top: e.top - t.height - n.y,
    right: e.right - t.width + n.x,
    bottom: e.bottom - t.height + n.y,
    left: e.left - t.width - n.x
  };
}
function si(e) {
  return [me, Te, Ce, pe].some(function(t) {
    return e[t] >= 0;
  });
}
function ec(e) {
  var t = e.state, n = e.name, r = t.rects.reference, i = t.rects.popper, a = t.modifiersData.preventOverflow, l = dn(t, {
    elementContext: "reference"
  }), c = dn(t, {
    altBoundary: !0
  }), d = oi(l, r), p = oi(c, i, a), f = si(d), h = si(p);
  t.modifiersData[n] = {
    referenceClippingOffsets: d,
    popperEscapeOffsets: p,
    isReferenceHidden: f,
    hasPopperEscaped: h
  }, t.attributes.popper = Object.assign({}, t.attributes.popper, {
    "data-popper-reference-hidden": f,
    "data-popper-escaped": h
  });
}
const tc = {
  name: "hide",
  enabled: !0,
  phase: "main",
  requiresIfExists: ["preventOverflow"],
  fn: ec
};
function nc(e, t, n) {
  var r = Ve(e), i = [pe, me].indexOf(r) >= 0 ? -1 : 1, a = typeof n == "function" ? n(Object.assign({}, t, {
    placement: e
  })) : n, l = a[0], c = a[1];
  return l = l || 0, c = (c || 0) * i, [pe, Te].indexOf(r) >= 0 ? {
    x: c,
    y: l
  } : {
    x: l,
    y: c
  };
}
function rc(e) {
  var t = e.state, n = e.options, r = e.name, i = n.offset, a = i === void 0 ? [0, 0] : i, l = Yi.reduce(function(f, h) {
    return f[h] = nc(h, t.rects, a), f;
  }, {}), c = l[t.placement], d = c.x, p = c.y;
  t.modifiersData.popperOffsets != null && (t.modifiersData.popperOffsets.x += d, t.modifiersData.popperOffsets.y += p), t.modifiersData[r] = l;
}
const ic = {
  name: "offset",
  enabled: !0,
  phase: "main",
  requires: ["popperOffsets"],
  fn: rc
};
function ac(e) {
  var t = e.state, n = e.name;
  t.modifiersData[n] = ta({
    reference: t.rects.reference,
    element: t.rects.popper,
    placement: t.placement
  });
}
const oc = {
  name: "popperOffsets",
  enabled: !0,
  phase: "read",
  fn: ac,
  data: {}
};
function sc(e) {
  return e === "x" ? "y" : "x";
}
function cc(e) {
  var t = e.state, n = e.options, r = e.name, i = n.mainAxis, a = i === void 0 ? !0 : i, l = n.altAxis, c = l === void 0 ? !1 : l, d = n.boundary, p = n.rootBoundary, f = n.altBoundary, h = n.padding, b = n.tether, g = b === void 0 ? !0 : b, j = n.tetherOffset, O = j === void 0 ? 0 : j, E = dn(t, {
    boundary: d,
    rootBoundary: p,
    padding: h,
    altBoundary: f
  }), N = Ve(t.placement), T = Yt(t.placement), B = !T, u = Dr(N), R = sc(u), _ = t.modifiersData.popperOffsets, W = t.rects.reference, G = t.rects.popper, A = typeof O == "function" ? O(Object.assign({}, t.rects, {
    placement: t.placement
  })) : O, L = typeof A == "number" ? {
    mainAxis: A,
    altAxis: A
  } : Object.assign({
    mainAxis: 0,
    altAxis: 0
  }, A), U = t.modifiersData.offset ? t.modifiersData.offset[t.placement] : null, V = {
    x: 0,
    y: 0
  };
  if (_) {
    if (a) {
      var I, P = u === "y" ? me : pe, S = u === "y" ? Ce : Te, X = u === "y" ? "height" : "width", K = _[u], Y = K + E[P], ee = K - E[S], Z = g ? -G[X] / 2 : 0, Re = T === Ht ? W[X] : G[X], ve = T === Ht ? -G[X] : -W[X], Ae = t.elements.arrow, ke = g && Ae ? Pr(Ae) : {
        width: 0,
        height: 0
      }, he = t.modifiersData["arrow#persistent"] ? t.modifiersData["arrow#persistent"].padding : Zi(), ge = he[P], Ie = he[S], ue = an(0, W[X], ke[X]), Me = B ? W[X] / 2 - Z - ue - ge - L.mainAxis : Re - ue - ge - L.mainAxis, ye = B ? -W[X] / 2 + Z + ue + Ie + L.mainAxis : ve + ue + Ie + L.mainAxis, at = t.elements.arrow && vn(t.elements.arrow), gt = at ? u === "y" ? at.clientTop || 0 : at.clientLeft || 0 : 0, yt = (I = U == null ? void 0 : U[u]) != null ? I : 0, It = K + Me - yt - gt, qe = K + ye - yt, Ne = an(g ? Rn(Y, It) : Y, K, g ? Nt(ee, qe) : ee);
      _[u] = Ne, V[u] = Ne - K;
    }
    if (c) {
      var ot, bt = u === "x" ? me : pe, Ye = u === "x" ? Ce : Te, $e = _[R], Le = R === "y" ? "height" : "width", Ge = $e + E[bt], Ke = $e - E[Ye], _t = [me, pe].indexOf(N) !== -1, xt = (ot = U == null ? void 0 : U[R]) != null ? ot : 0, wt = _t ? Ge : $e - W[Le] - G[Le] - xt + L.altAxis, jt = _t ? $e + W[Le] + G[Le] - xt - L.altAxis : Ke, Mt = g && _t ? Ps(wt, $e, jt) : an(g ? wt : Ge, $e, g ? jt : Ke);
      _[R] = Mt, V[R] = Mt - $e;
    }
    t.modifiersData[r] = V;
  }
}
const lc = {
  name: "preventOverflow",
  enabled: !0,
  phase: "main",
  fn: cc,
  requiresIfExists: ["offset"]
};
function uc(e) {
  return {
    scrollLeft: e.scrollLeft,
    scrollTop: e.scrollTop
  };
}
function dc(e) {
  return e === we(e) || !Oe(e) ? Rr(e) : uc(e);
}
function fc(e) {
  var t = e.getBoundingClientRect(), n = Ut(t.width) / e.offsetWidth || 1, r = Ut(t.height) / e.offsetHeight || 1;
  return n !== 1 || r !== 1;
}
function mc(e, t, n) {
  n === void 0 && (n = !1);
  var r = Oe(t), i = Oe(t) && fc(t), a = vt(t), l = qt(e, i, n), c = {
    scrollLeft: 0,
    scrollTop: 0
  }, d = {
    x: 0,
    y: 0
  };
  return (r || !r && !n) && ((He(t) !== "body" || // https://github.com/popperjs/popper-core/issues/1078
  Ir(a)) && (c = dc(t)), Oe(t) ? (d = qt(t, !0), d.x += t.clientLeft, d.y += t.clientTop) : a && (d.x = Ar(a))), {
    x: l.left + c.scrollLeft - d.x,
    y: l.top + c.scrollTop - d.y,
    width: l.width,
    height: l.height
  };
}
function pc(e) {
  var t = /* @__PURE__ */ new Map(), n = /* @__PURE__ */ new Set(), r = [];
  e.forEach(function(a) {
    t.set(a.name, a);
  });
  function i(a) {
    n.add(a.name);
    var l = [].concat(a.requires || [], a.requiresIfExists || []);
    l.forEach(function(c) {
      if (!n.has(c)) {
        var d = t.get(c);
        d && i(d);
      }
    }), r.push(a);
  }
  return e.forEach(function(a) {
    n.has(a.name) || i(a);
  }), r;
}
function vc(e) {
  var t = pc(e);
  return Ts.reduce(function(n, r) {
    return n.concat(t.filter(function(i) {
      return i.phase === r;
    }));
  }, []);
}
function hc(e) {
  var t;
  return function() {
    return t || (t = new Promise(function(n) {
      Promise.resolve().then(function() {
        t = void 0, n(e());
      });
    })), t;
  };
}
function gc(e) {
  var t = e.reduce(function(n, r) {
    var i = n[r.name];
    return n[r.name] = i ? Object.assign({}, i, r, {
      options: Object.assign({}, i.options, r.options),
      data: Object.assign({}, i.data, r.data)
    }) : r, n;
  }, {});
  return Object.keys(t).map(function(n) {
    return t[n];
  });
}
var ci = {
  placement: "bottom",
  modifiers: [],
  strategy: "absolute"
};
function li() {
  for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)
    t[n] = arguments[n];
  return !t.some(function(r) {
    return !(r && typeof r.getBoundingClientRect == "function");
  });
}
function yc(e) {
  e === void 0 && (e = {});
  var t = e, n = t.defaultModifiers, r = n === void 0 ? [] : n, i = t.defaultOptions, a = i === void 0 ? ci : i;
  return function(c, d, p) {
    p === void 0 && (p = a);
    var f = {
      placement: "bottom",
      orderedModifiers: [],
      options: Object.assign({}, ci, a),
      modifiersData: {},
      elements: {
        reference: c,
        popper: d
      },
      attributes: {},
      styles: {}
    }, h = [], b = !1, g = {
      state: f,
      setOptions: function(N) {
        var T = typeof N == "function" ? N(f.options) : N;
        O(), f.options = Object.assign({}, a, f.options, T), f.scrollParents = {
          reference: Pt(c) ? on(c) : c.contextElement ? on(c.contextElement) : [],
          popper: on(d)
        };
        var B = vc(gc([].concat(r, f.options.modifiers)));
        return f.orderedModifiers = B.filter(function(u) {
          return u.enabled;
        }), j(), g.update();
      },
      // Sync update – it will always be executed, even if not necessary. This
      // is useful for low frequency updates where sync behavior simplifies the
      // logic.
      // For high frequency updates (e.g. `resize` and `scroll` events), always
      // prefer the async Popper#update method
      forceUpdate: function() {
        if (!b) {
          var N = f.elements, T = N.reference, B = N.popper;
          if (li(T, B)) {
            f.rects = {
              reference: mc(T, vn(B), f.options.strategy === "fixed"),
              popper: Pr(B)
            }, f.reset = !1, f.placement = f.options.placement, f.orderedModifiers.forEach(function(L) {
              return f.modifiersData[L.name] = Object.assign({}, L.data);
            });
            for (var u = 0; u < f.orderedModifiers.length; u++) {
              if (f.reset === !0) {
                f.reset = !1, u = -1;
                continue;
              }
              var R = f.orderedModifiers[u], _ = R.fn, W = R.options, G = W === void 0 ? {} : W, A = R.name;
              typeof _ == "function" && (f = _({
                state: f,
                options: G,
                name: A,
                instance: g
              }) || f);
            }
          }
        }
      },
      // Async and optimistically optimized update – it will not be executed if
      // not necessary (debounced to run at most once-per-tick)
      update: hc(function() {
        return new Promise(function(E) {
          g.forceUpdate(), E(f);
        });
      }),
      destroy: function() {
        O(), b = !0;
      }
    };
    if (!li(c, d))
      return g;
    g.setOptions(p).then(function(E) {
      !b && p.onFirstUpdate && p.onFirstUpdate(E);
    });
    function j() {
      f.orderedModifiers.forEach(function(E) {
        var N = E.name, T = E.options, B = T === void 0 ? {} : T, u = E.effect;
        if (typeof u == "function") {
          var R = u({
            state: f,
            name: N,
            instance: g,
            options: B
          }), _ = function() {
          };
          h.push(R || _);
        }
      });
    }
    function O() {
      h.forEach(function(E) {
        return E();
      }), h = [];
    }
    return g;
  };
}
var bc = [Bs, oc, Fs, Gi, ic, Qs, lc, Is, tc], na = /* @__PURE__ */ yc({
  defaultModifiers: bc
}), _c = typeof Element < "u", xc = typeof Map == "function", wc = typeof Set == "function", jc = typeof ArrayBuffer == "function" && !!ArrayBuffer.isView;
function Pn(e, t) {
  if (e === t) return !0;
  if (e && t && typeof e == "object" && typeof t == "object") {
    if (e.constructor !== t.constructor) return !1;
    var n, r, i;
    if (Array.isArray(e)) {
      if (n = e.length, n != t.length) return !1;
      for (r = n; r-- !== 0; )
        if (!Pn(e[r], t[r])) return !1;
      return !0;
    }
    var a;
    if (xc && e instanceof Map && t instanceof Map) {
      if (e.size !== t.size) return !1;
      for (a = e.entries(); !(r = a.next()).done; )
        if (!t.has(r.value[0])) return !1;
      for (a = e.entries(); !(r = a.next()).done; )
        if (!Pn(r.value[1], t.get(r.value[0]))) return !1;
      return !0;
    }
    if (wc && e instanceof Set && t instanceof Set) {
      if (e.size !== t.size) return !1;
      for (a = e.entries(); !(r = a.next()).done; )
        if (!t.has(r.value[0])) return !1;
      return !0;
    }
    if (jc && ArrayBuffer.isView(e) && ArrayBuffer.isView(t)) {
      if (n = e.length, n != t.length) return !1;
      for (r = n; r-- !== 0; )
        if (e[r] !== t[r]) return !1;
      return !0;
    }
    if (e.constructor === RegExp) return e.source === t.source && e.flags === t.flags;
    if (e.valueOf !== Object.prototype.valueOf && typeof e.valueOf == "function" && typeof t.valueOf == "function") return e.valueOf() === t.valueOf();
    if (e.toString !== Object.prototype.toString && typeof e.toString == "function" && typeof t.toString == "function") return e.toString() === t.toString();
    if (i = Object.keys(e), n = i.length, n !== Object.keys(t).length) return !1;
    for (r = n; r-- !== 0; )
      if (!Object.prototype.hasOwnProperty.call(t, i[r])) return !1;
    if (_c && e instanceof Element) return !1;
    for (r = n; r-- !== 0; )
      if (!((i[r] === "_owner" || i[r] === "__v" || i[r] === "__o") && e.$$typeof) && !Pn(e[i[r]], t[i[r]]))
        return !1;
    return !0;
  }
  return e !== e && t !== t;
}
var Ec = function(t, n) {
  try {
    return Pn(t, n);
  } catch (r) {
    if ((r.message || "").match(/stack|recursion/i))
      return console.warn("react-fast-compare cannot handle circular refs"), !1;
    throw r;
  }
};
const Oc = /* @__PURE__ */ qa(Ec);
var Cc = [], Tc = function(t, n, r) {
  r === void 0 && (r = {});
  var i = J.useRef(null), a = {
    onFirstUpdate: r.onFirstUpdate,
    placement: r.placement || "bottom",
    strategy: r.strategy || "absolute",
    modifiers: r.modifiers || Cc
  }, l = J.useState({
    styles: {
      popper: {
        position: a.strategy,
        left: "0",
        top: "0"
      },
      arrow: {
        position: "absolute"
      }
    },
    attributes: {}
  }), c = l[0], d = l[1], p = J.useMemo(function() {
    return {
      name: "updateState",
      enabled: !0,
      phase: "write",
      fn: function(g) {
        var j = g.state, O = Object.keys(j.elements);
        kr.flushSync(function() {
          d({
            styles: Qr(O.map(function(E) {
              return [E, j.styles[E] || {}];
            })),
            attributes: Qr(O.map(function(E) {
              return [E, j.attributes[E]];
            }))
          });
        });
      },
      requires: ["computeStyles"]
    };
  }, []), f = J.useMemo(function() {
    var b = {
      onFirstUpdate: a.onFirstUpdate,
      placement: a.placement,
      strategy: a.strategy,
      modifiers: [].concat(a.modifiers, [p, {
        name: "applyStyles",
        enabled: !1
      }])
    };
    return Oc(i.current, b) ? i.current || b : (i.current = b, b);
  }, [a.onFirstUpdate, a.placement, a.strategy, a.modifiers, p]), h = J.useRef();
  return ei(function() {
    h.current && h.current.setOptions(f);
  }, [f]), ei(function() {
    if (!(t == null || n == null)) {
      var b = r.createPopper || na, g = b(t, n, f);
      return h.current = g, function() {
        g.destroy(), h.current = null;
      };
    }
  }, [t, n, r.createPopper]), {
    state: h.current ? h.current.state : null,
    styles: c.styles,
    attributes: c.attributes,
    update: h.current ? h.current.update : null,
    forceUpdate: h.current ? h.current.forceUpdate : null
  };
};
const Sc = "_srOnly_t30sa_2", kc = {
  srOnly: Sc
}, er = { current: null }, Nc = () => {
  if (er.current === null) {
    const e = document.createElement("div");
    document.body.append(e), er.current = e;
  }
  return er.current;
}, ra = {
  srOnly: kc.srOnly
}, $c = "_arrowShape_1a4rs_1", Pc = "_container_1a4rs_14", Dc = "_arrow_1a4rs_1", tr = {
  arrowShape: $c,
  container: Pc,
  arrow: Dc
}, zn = (e) => {
  const [t, n] = Pe(null), [r, i] = Pe(null), a = zn.styles.outset, { styles: l, attributes: c, update: d } = Tc(e.target, t, {
    placement: e.placement ?? "top",
    modifiers: [
      { name: "offset", options: { offset: [0, 8] } },
      { name: "arrow", options: { element: r } }
    ]
  }), { onOutsideClick: p } = e;
  et(() => {
    if (t === null) return;
    const h = (b) => {
      b.target instanceof Node && (t.contains(b.target) || p == null || p());
    };
    return window.setTimeout(() => {
      document.addEventListener("click", h);
    }, 0), () => void document.removeEventListener("click", h);
  }, [t, p]), et(() => {
    d == null || d();
  }, [d]);
  const f = /* @__PURE__ */ s.jsxs(
    "div",
    {
      ref: n,
      style: l.popper,
      className: [tr.container, a.content].join(" "),
      ...c.popper,
      children: [
        e.children,
        /* @__PURE__ */ s.jsx(
          "div",
          {
            style: l.arrow,
            ref: i,
            className: tr.arrow,
            ...c.arrow,
            children: /* @__PURE__ */ s.jsx("div", { className: [tr.arrowShape, a.arrow].join(" ") })
          }
        )
      ]
    }
  );
  return Ai(f, Nc());
};
zn.styles = {
  outset: {
    content: [
      Gt.dropStrong,
      H.px1,
      H.strong,
      _e.strong
    ].join(" "),
    arrow: [H.px1, H.strong, _e.strong].join(" ")
  }
};
const hn = (e) => {
  const [t, n] = Pe(!1), [r, i] = Pe(null), a = cn(() => n((d) => !d), []), l = cn(() => void n(!1), []), c = e.TargetWrapper ?? hn.targetWrappers.blockContent;
  return /* @__PURE__ */ s.jsxs(Va, { children: [
    /* @__PURE__ */ s.jsx(c, { setTarget: i, children: e.target({ toggle: a, opened: t }) }),
    t && r && /* @__PURE__ */ s.jsx(
      zn,
      {
        children: e.content({ close: l }),
        target: r,
        placement: e.placement,
        onOutsideClick: l
      }
    )
  ] });
};
hn.styles = zn.styles;
hn.targetWrappers = {
  block: (e) => /* @__PURE__ */ s.jsx("div", { ref: e.setTarget, children: e.children }),
  blockContent: (e) => /* @__PURE__ */ s.jsx(
    "div",
    {
      ref: e.setTarget,
      children: e.children,
      style: { width: "fit-content" }
    }
  ),
  inline: (e) => /* @__PURE__ */ s.jsx("span", { ref: e.setTarget, children: e.children })
};
const ia = (e) => /* @__PURE__ */ s.jsx(
  hn,
  {
    placement: e.placement,
    content: () => /* @__PURE__ */ s.jsxs("div", { children: [
      /* @__PURE__ */ s.jsx(xe, { size: 8 }),
      e.items.map((t, n) => /* @__PURE__ */ s.jsx(Ui, { item: t }, n)),
      /* @__PURE__ */ s.jsx(xe, { size: 8 })
    ] }),
    target: (t) => /* @__PURE__ */ s.jsx(
      k,
      {
        ...e.button,
        selected: t.opened,
        onClick: t.toggle
      }
    )
  }
), Rc = "_p_17liy_1", Ac = "_strong_17liy_5", Ic = "_big_17liy_13", Mc = "_failureWeak_17liy_17", Lc = "_failureStrong_17liy_21", zc = "_successWeak_17liy_25", Fc = "_successStrong_17liy_29", Wc = "_highlightWeak_17liy_33", Bc = "_highlightStrong_17liy_37", Vc = "_normal_17liy_41", Hc = "_muted_17liy_45", ie = {
  p: Rc,
  strong: Ac,
  break: "_break_17liy_9",
  big: Ic,
  failureWeak: Mc,
  failureStrong: Lc,
  successWeak: zc,
  successStrong: Fc,
  highlightWeak: Wc,
  highlightStrong: Bc,
  normal: Vc,
  muted: Hc
}, rt = {
  strong: ie.strong,
  p: ie.p,
  break: ie.break,
  big: ie.big,
  // ===
  normal: ie.normal,
  muted: ie.muted,
  highlightStrong: ie.highlightStrong,
  highlightWeak: ie.highlightWeak,
  successStrong: ie.successStrong,
  successWeak: ie.successWeak,
  failureStrong: ie.failureStrong,
  failureWeak: ie.failureWeak
}, aa = ({ children: e }) => /* @__PURE__ */ s.jsx("p", { className: ie.p, children: e }), Ap = ({ children: e }) => /* @__PURE__ */ s.jsx("strong", { className: ie.strong, children: e }), Ip = ({ children: e }) => /* @__PURE__ */ s.jsx("div", { className: ie.muted, children: e }), Mp = ({ children: e }) => /* @__PURE__ */ s.jsx("span", { className: ie.muted, children: e }), oa = (e, t) => {
  const n = {};
  let r;
  for (r in e)
    t.includes(r) || (n[r] = e[r]);
  return n;
}, Uc = "_main_6crox_1", qc = {
  main: Uc
}, Yc = "_container_xzbja_1", Gc = "_input_xzbja_6", Kc = "_icon_xzbja_10", Xc = "_largeMain_xzbja_20", Zc = "_largeMainWithIcon_xzbja_21", Jc = "_largeIcon_xzbja_36", Qc = "_largeColor_xzbja_40", el = "_mediumMain_xzbja_45", tl = "_mediumMainWithIcon_xzbja_46", nl = "_mediumIcon_xzbja_61", rl = "_mediumColor_xzbja_65", il = "_smallMain_xzbja_71", al = "_smallMainWithIcon_xzbja_72", ol = "_smallIcon_xzbja_86", sl = "_smallColor_xzbja_90", se = {
  container: Yc,
  input: Gc,
  icon: Kc,
  largeMain: Xc,
  largeMainWithIcon: Zc,
  largeIcon: Jc,
  largeColor: Qc,
  mediumMain: el,
  mediumMainWithIcon: tl,
  mediumIcon: nl,
  mediumColor: rl,
  smallMain: il,
  smallMainWithIcon: al,
  smallIcon: ol,
  smallColor: sl
}, cl = "_main_1843i_1", ll = {
  main: cl
}, ul = (e) => {
  const t = e.style ?? Se.styles.outset, n = [se.input, Rt.normal, t.main], r = e.size ?? Se.sizes.medium;
  return e.type === "color" ? n.push(r.mainColor) : n.push(e.icon ? r.mainWithIcon : r.main), n.join(" ");
}, dl = {
  onChangeSetValue: '"onChange" and "setValue" must not be defined at the same time'
}, fl = (e) => {
  if (e.onChange !== void 0 && e.setValue !== void 0)
    throw Error(dl.onChangeSetValue);
}, ml = (e, t) => {
  var i;
  fl(e);
  const n = e.size ?? Se.sizes.medium, r = oa(e, [
    "list",
    "className",
    "size",
    "style",
    "icon",
    "defaultValue",
    "value",
    "setValue",
    "onChange"
  ]);
  return /* @__PURE__ */ s.jsxs("div", { className: se.container, children: [
    /* @__PURE__ */ s.jsx(
      "input",
      {
        ...r,
        ref: t,
        defaultValue: e.defaultValue,
        value: e.value,
        onChange: (a) => {
          var l, c;
          (l = e.onChange) == null || l.call(e, a), (c = e.setValue) == null || c.call(e, a.currentTarget.value);
        },
        className: ul(e),
        list: typeof e.list == "string" ? e.list : ((i = e.list) == null ? void 0 : i.id) ?? void 0
      }
    ),
    e.icon && /* @__PURE__ */ s.jsx("div", { className: [se.icon, rt.muted, n.icon].join(" "), children: /* @__PURE__ */ s.jsx(mt, { display: "block", component: e.icon, size: n.iconSize }) }),
    typeof e.list == "object" && /* @__PURE__ */ s.jsx("datalist", { id: e.list.id, children: e.list.values.map((a) => /* @__PURE__ */ s.jsx("option", { value: a }, a)) })
  ] });
}, Se = le.forwardRef(ml);
Se.styles = {
  outset: { main: [ll.main, H.radius].join(" ") },
  flat: { main: [qc.main].join(" ") }
};
Se.sizes = {
  large: {
    main: se.largeMain,
    mainWithIcon: se.largeMainWithIcon,
    icon: se.largeIcon,
    iconSize: 16,
    mainColor: se.largeColor
  },
  medium: {
    main: se.mediumMain,
    mainWithIcon: se.mediumMainWithIcon,
    icon: se.mediumIcon,
    iconSize: 16,
    mainColor: se.mediumColor
  },
  small: {
    main: se.smallMain,
    mainWithIcon: se.smallMainWithIcon,
    icon: se.smallIcon,
    iconSize: 12,
    mainColor: se.smallColor
  }
};
var sa = {
  color: void 0,
  size: void 0,
  className: void 0,
  style: void 0,
  attr: void 0
}, ui = le.createContext && /* @__PURE__ */ le.createContext(sa), pl = ["attr", "size", "title"];
function vl(e, t) {
  if (e == null) return {};
  var n = hl(e, t), r, i;
  if (Object.getOwnPropertySymbols) {
    var a = Object.getOwnPropertySymbols(e);
    for (i = 0; i < a.length; i++)
      r = a[i], !(t.indexOf(r) >= 0) && Object.prototype.propertyIsEnumerable.call(e, r) && (n[r] = e[r]);
  }
  return n;
}
function hl(e, t) {
  if (e == null) return {};
  var n = {};
  for (var r in e)
    if (Object.prototype.hasOwnProperty.call(e, r)) {
      if (t.indexOf(r) >= 0) continue;
      n[r] = e[r];
    }
  return n;
}
function An() {
  return An = Object.assign ? Object.assign.bind() : function(e) {
    for (var t = 1; t < arguments.length; t++) {
      var n = arguments[t];
      for (var r in n)
        Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r]);
    }
    return e;
  }, An.apply(this, arguments);
}
function di(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var r = Object.getOwnPropertySymbols(e);
    t && (r = r.filter(function(i) {
      return Object.getOwnPropertyDescriptor(e, i).enumerable;
    })), n.push.apply(n, r);
  }
  return n;
}
function In(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = arguments[t] != null ? arguments[t] : {};
    t % 2 ? di(Object(n), !0).forEach(function(r) {
      gl(e, r, n[r]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : di(Object(n)).forEach(function(r) {
      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(n, r));
    });
  }
  return e;
}
function gl(e, t, n) {
  return t = yl(t), t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e;
}
function yl(e) {
  var t = bl(e, "string");
  return typeof t == "symbol" ? t : t + "";
}
function bl(e, t) {
  if (typeof e != "object" || !e) return e;
  var n = e[Symbol.toPrimitive];
  if (n !== void 0) {
    var r = n.call(e, t);
    if (typeof r != "object") return r;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (t === "string" ? String : Number)(e);
}
function ca(e) {
  return e && e.map((t, n) => /* @__PURE__ */ le.createElement(t.tag, In({
    key: n
  }, t.attr), ca(t.child)));
}
function je(e) {
  return (t) => /* @__PURE__ */ le.createElement(_l, An({
    attr: In({}, e.attr)
  }, t), ca(e.child));
}
function _l(e) {
  var t = (n) => {
    var {
      attr: r,
      size: i,
      title: a
    } = e, l = vl(e, pl), c = i || n.size || "1em", d;
    return n.className && (d = n.className), e.className && (d = (d ? d + " " : "") + e.className), /* @__PURE__ */ le.createElement("svg", An({
      stroke: "currentColor",
      fill: "currentColor",
      strokeWidth: "0"
    }, n.attr, r, l, {
      className: d,
      style: In(In({
        color: e.color || n.color
      }, n.style), e.style),
      height: c,
      width: c,
      xmlns: "http://www.w3.org/2000/svg"
    }), a && /* @__PURE__ */ le.createElement("title", null, a), e.children);
  };
  return ui !== void 0 ? /* @__PURE__ */ le.createElement(ui.Consumer, null, (n) => t(n)) : t(sa);
}
function xl(e) {
  return je({ attr: { viewBox: "0 0 24 24" }, child: [{ tag: "path", attr: { d: "M21.03 5.72a.75.75 0 0 1 0 1.06l-11.5 11.5a.747.747 0 0 1-1.072-.012l-5.5-5.75a.75.75 0 1 1 1.084-1.036l4.97 5.195L19.97 5.72a.75.75 0 0 1 1.06 0Z" }, child: [] }] })(e);
}
function wl(e) {
  return je({ attr: { viewBox: "0 0 24 24" }, child: [{ tag: "path", attr: { d: "M5.22 8.22a.749.749 0 0 0 0 1.06l6.25 6.25a.749.749 0 0 0 1.06 0l6.25-6.25a.749.749 0 1 0-1.06-1.06L12 13.939 6.28 8.22a.749.749 0 0 0-1.06 0Z" }, child: [] }] })(e);
}
function jl(e) {
  return je({ attr: { viewBox: "0 0 24 24" }, child: [{ tag: "path", attr: { d: "M15.28 5.22a.75.75 0 0 1 0 1.06L9.56 12l5.72 5.72a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215l-6.25-6.25a.75.75 0 0 1 0-1.06l6.25-6.25a.75.75 0 0 1 1.06 0Z" }, child: [] }] })(e);
}
function El(e) {
  return je({ attr: { viewBox: "0 0 24 24" }, child: [{ tag: "path", attr: { d: "M8.72 18.78a.75.75 0 0 1 0-1.06L14.44 12 8.72 6.28a.751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018l6.25 6.25a.75.75 0 0 1 0 1.06l-6.25 6.25a.75.75 0 0 1-1.06 0Z" }, child: [] }] })(e);
}
function Ol(e) {
  return je({ attr: { viewBox: "0 0 24 24" }, child: [{ tag: "path", attr: { d: "M18.78 15.78a.749.749 0 0 1-1.06 0L12 10.061 6.28 15.78a.749.749 0 1 1-1.06-1.06l6.25-6.25a.749.749 0 0 1 1.06 0l6.25 6.25a.749.749 0 0 1 0 1.06Z" }, child: [] }] })(e);
}
function Cl(e) {
  return je({ attr: { viewBox: "0 0 24 24" }, child: [{ tag: "path", attr: { d: "M4.5 12.75a.75.75 0 0 1 .75-.75h13.5a.75.75 0 0 1 0 1.5H5.25a.75.75 0 0 1-.75-.75Z" }, child: [] }] })(e);
}
function Tl(e) {
  return je({ attr: { viewBox: "0 0 24 24" }, child: [{ tag: "path", attr: { d: "M12 18a6 6 0 1 0 0-12 6 6 0 0 0 0 12Z" }, child: [] }] })(e);
}
function Sl(e) {
  return je({ attr: { viewBox: "0 0 24 24" }, child: [{ tag: "path", attr: { d: "M20 14a2 2 0 1 1-.001-3.999A2 2 0 0 1 20 14ZM6 12a2 2 0 1 1-3.999.001A2 2 0 0 1 6 12Zm8 0a2 2 0 1 1-3.999.001A2 2 0 0 1 14 12Z" }, child: [] }] })(e);
}
function kl(e) {
  return je({ attr: { viewBox: "0 0 24 24" }, child: [{ tag: "path", attr: { d: "M11.646 15.146 5.854 9.354a.5.5 0 0 1 .353-.854h11.586a.5.5 0 0 1 .353.854l-5.793 5.792a.5.5 0 0 1-.707 0Z" }, child: [] }] })(e);
}
function Nl(e) {
  return je({ attr: { viewBox: "0 0 24 24" }, child: [{ tag: "path", attr: { d: "M5.72 5.72a.75.75 0 0 1 1.06 0L12 10.94l5.22-5.22a.749.749 0 0 1 1.275.326.749.749 0 0 1-.215.734L13.06 12l5.22 5.22a.749.749 0 0 1-.326 1.275.749.749 0 0 1-.734-.215L12 13.06l-5.22 5.22a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042L10.94 12 5.72 6.78a.75.75 0 0 1 0-1.06Z" }, child: [] }] })(e);
}
function $l(e) {
  return je({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM11.0026 16L18.0737 8.92893L16.6595 7.51472L11.0026 13.1716L8.17421 10.3431L6.75999 11.7574L11.0026 16Z" }, child: [] }] })(e);
}
function Pl(e) {
  return je({ attr: { viewBox: "0 0 24 24", fill: "currentColor" }, child: [{ tag: "path", attr: { d: "M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM11 15V17H13V15H11ZM11 7V13H13V7H11Z" }, child: [] }] })(e);
}
const Dl = (e) => je({ attr: { viewBox: "0 0 16 16" }, child: [] })(
  e
), ae = {
  blank: Dl,
  caret: kl,
  check: xl,
  chevronDown: wl,
  chevronLeft: jl,
  chevronRight: El,
  chevronUp: Ol,
  cross: Nl,
  dash: Cl,
  dot: Tl,
  kebab: Sl,
  error: Pl,
  success: $l
}, Rl = "_container_be0tn_1", Al = "_fill_be0tn_6", Il = "_select_be0tn_10", Ml = "_icon_be0tn_29", Ll = "_mediumSelect_be0tn_40", zl = "_mediumIcon_be0tn_46", Fl = "_smallSelect_be0tn_50", Wl = "_smallIcon_be0tn_56", ft = {
  container: Rl,
  fill: Al,
  select: Il,
  icon: Ml,
  mediumSelect: Ll,
  mediumIcon: zl,
  smallSelect: Fl,
  smallIcon: Wl
}, Bl = (e) => {
  const t = e.style ?? De.styles.outset, n = e.size ?? De.sizes.medium, r = e.fill ? ft.fill : "";
  return {
    select: [ft.select, t.select, n.select, Rt.normal].join(" "),
    container: [ft.container, r].join(" "),
    icon: [ft.icon, n.icon].join(" ")
  };
}, Vl = (e) => /* @__PURE__ */ s.jsx(
  "option",
  {
    value: e.id,
    disabled: e.disabled,
    children: e.label
  },
  e.id
), fi = (e, t) => {
  var n;
  return (n = e.options.find((r) => r.value === t)) == null ? void 0 : n.id;
}, Hl = (e) => (t) => {
  if (e.setValue === void 0) return;
  const n = t.target.value, r = e.options.find((i) => i.id === n);
  if (!r) throw Error(`Option not found: "${n}"`);
  e.setValue(r.value);
}, De = (e) => {
  const t = Bl(e), n = fi(e, e.value), r = fi(e, e.defaultValue);
  return /* @__PURE__ */ s.jsxs("div", { className: t.container, children: [
    /* @__PURE__ */ s.jsx(
      "select",
      {
        id: e.id,
        className: t.select,
        disabled: e.disabled,
        children: e.options.map(Vl),
        defaultValue: r,
        ref: e.forwardedRef,
        value: n,
        required: e.required,
        onChange: Hl(e)
      }
    ),
    /* @__PURE__ */ s.jsx("span", { className: t.icon, children: /* @__PURE__ */ s.jsx(mt, { size: 12, display: "block", component: ae.caret }) })
  ] });
};
De.styles = {
  outset: {
    select: [
      H.radius,
      ln.none.outset.mainClassName,
      hr.main
    ].join(" ")
  },
  flat: {
    select: [vr.main, ln.none.flat.mainClassName].join(" ")
  }
};
De.sizes = {
  medium: {
    select: ft.mediumSelect,
    icon: ft.mediumIcon
  },
  small: {
    select: ft.smallSelect,
    icon: ft.smallIcon
  }
};
De.toStringOption = (e) => ({
  value: e,
  id: e,
  label: e
});
De.toNumberOption = (e) => ({
  value: e,
  id: e.toString(),
  label: e.toString()
});
var Ul = "tippy-content", ql = "tippy-backdrop", Yl = "tippy-arrow", Gl = "tippy-svg-arrow", Ct = {
  passive: !0,
  capture: !0
}, la = function() {
  return document.body;
};
function Kl(e, t) {
  return {}.hasOwnProperty.call(e, t);
}
function nr(e, t, n) {
  if (Array.isArray(e)) {
    var r = e[t];
    return r ?? (Array.isArray(n) ? n[t] : n);
  }
  return e;
}
function Mr(e, t) {
  var n = {}.toString.call(e);
  return n.indexOf("[object") === 0 && n.indexOf(t + "]") > -1;
}
function ua(e, t) {
  return typeof e == "function" ? e.apply(void 0, t) : e;
}
function mi(e, t) {
  if (t === 0)
    return e;
  var n;
  return function(r) {
    clearTimeout(n), n = setTimeout(function() {
      e(r);
    }, t);
  };
}
function Xl(e, t) {
  var n = Object.assign({}, e);
  return t.forEach(function(r) {
    delete n[r];
  }), n;
}
function Zl(e) {
  return e.split(/\s+/).filter(Boolean);
}
function Vt(e) {
  return [].concat(e);
}
function pi(e, t) {
  e.indexOf(t) === -1 && e.push(t);
}
function Jl(e) {
  return e.filter(function(t, n) {
    return e.indexOf(t) === n;
  });
}
function Ql(e) {
  return e.split("-")[0];
}
function Mn(e) {
  return [].slice.call(e);
}
function vi(e) {
  return Object.keys(e).reduce(function(t, n) {
    return e[n] !== void 0 && (t[n] = e[n]), t;
  }, {});
}
function eu() {
  return document.createElement("div");
}
function br(e) {
  return ["Element", "Fragment"].some(function(t) {
    return Mr(e, t);
  });
}
function tu(e) {
  return Mr(e, "NodeList");
}
function nu(e) {
  return Mr(e, "MouseEvent");
}
function ru(e) {
  return !!(e && e._tippy && e._tippy.reference === e);
}
function iu(e) {
  return br(e) ? [e] : tu(e) ? Mn(e) : Array.isArray(e) ? e : Mn(document.querySelectorAll(e));
}
function rr(e, t) {
  e.forEach(function(n) {
    n && (n.style.transitionDuration = t + "ms");
  });
}
function hi(e, t) {
  e.forEach(function(n) {
    n && n.setAttribute("data-state", t);
  });
}
function au(e) {
  var t, n = Vt(e), r = n[0];
  return r != null && (t = r.ownerDocument) != null && t.body ? r.ownerDocument : document;
}
function ou(e, t) {
  var n = t.clientX, r = t.clientY;
  return e.every(function(i) {
    var a = i.popperRect, l = i.popperState, c = i.props, d = c.interactiveBorder, p = Ql(l.placement), f = l.modifiersData.offset;
    if (!f)
      return !0;
    var h = p === "bottom" ? f.top.y : 0, b = p === "top" ? f.bottom.y : 0, g = p === "right" ? f.left.x : 0, j = p === "left" ? f.right.x : 0, O = a.top - r + h > d, E = r - a.bottom - b > d, N = a.left - n + g > d, T = n - a.right - j > d;
    return O || E || N || T;
  });
}
function ir(e, t, n) {
  var r = t + "EventListener";
  ["transitionend", "webkitTransitionEnd"].forEach(function(i) {
    e[r](i, n);
  });
}
function gi(e, t) {
  for (var n = t; n; ) {
    var r;
    if (e.contains(n))
      return !0;
    n = n.getRootNode == null || (r = n.getRootNode()) == null ? void 0 : r.host;
  }
  return !1;
}
var We = {
  isTouch: !1
}, yi = 0;
function su() {
  We.isTouch || (We.isTouch = !0, window.performance && document.addEventListener("mousemove", da));
}
function da() {
  var e = performance.now();
  e - yi < 20 && (We.isTouch = !1, document.removeEventListener("mousemove", da)), yi = e;
}
function cu() {
  var e = document.activeElement;
  if (ru(e)) {
    var t = e._tippy;
    e.blur && !t.state.isVisible && e.blur();
  }
}
function lu() {
  document.addEventListener("touchstart", su, Ct), window.addEventListener("blur", cu);
}
var uu = typeof window < "u" && typeof document < "u", du = uu ? (
  // @ts-ignore
  !!window.msCrypto
) : !1;
function Bt(e) {
  var t = e === "destroy" ? "n already-" : " ";
  return [e + "() was called on a" + t + "destroyed instance. This is a no-op but", "indicates a potential memory leak."].join(" ");
}
function bi(e) {
  var t = /[ \t]{2,}/g, n = /^[ \t]*/gm;
  return e.replace(t, " ").replace(n, "").trim();
}
function fu(e) {
  return bi(`
  %ctippy.js

  %c` + bi(e) + `

  %c👷‍ This is a development-only message. It will be removed in production.
  `);
}
function fa(e) {
  return [
    fu(e),
    // title
    "color: #00C584; font-size: 1.3em; font-weight: bold;",
    // message
    "line-height: 1.5",
    // footer
    "color: #a6a095;"
  ];
}
var fn;
process.env.NODE_ENV !== "production" && mu();
function mu() {
  fn = /* @__PURE__ */ new Set();
}
function Ze(e, t) {
  if (e && !fn.has(t)) {
    var n;
    fn.add(t), (n = console).warn.apply(n, fa(t));
  }
}
function _r(e, t) {
  if (e && !fn.has(t)) {
    var n;
    fn.add(t), (n = console).error.apply(n, fa(t));
  }
}
function pu(e) {
  var t = !e, n = Object.prototype.toString.call(e) === "[object Object]" && !e.addEventListener;
  _r(t, ["tippy() was passed", "`" + String(e) + "`", "as its targets (first) argument. Valid types are: String, Element,", "Element[], or NodeList."].join(" ")), _r(n, ["tippy() was passed a plain object which is not supported as an argument", "for virtual positioning. Use props.getReferenceClientRect instead."].join(" "));
}
var ma = {
  animateFill: !1,
  followCursor: !1,
  inlinePositioning: !1,
  sticky: !1
}, vu = {
  allowHTML: !1,
  animation: "fade",
  arrow: !0,
  content: "",
  inertia: !1,
  maxWidth: 350,
  role: "tooltip",
  theme: "",
  zIndex: 9999
}, be = Object.assign({
  appendTo: la,
  aria: {
    content: "auto",
    expanded: "auto"
  },
  delay: 0,
  duration: [300, 250],
  getReferenceClientRect: null,
  hideOnClick: !0,
  ignoreAttributes: !1,
  interactive: !1,
  interactiveBorder: 2,
  interactiveDebounce: 0,
  moveTransition: "",
  offset: [0, 10],
  onAfterUpdate: function() {
  },
  onBeforeUpdate: function() {
  },
  onCreate: function() {
  },
  onDestroy: function() {
  },
  onHidden: function() {
  },
  onHide: function() {
  },
  onMount: function() {
  },
  onShow: function() {
  },
  onShown: function() {
  },
  onTrigger: function() {
  },
  onUntrigger: function() {
  },
  onClickOutside: function() {
  },
  placement: "top",
  plugins: [],
  popperOptions: {},
  render: null,
  showOnCreate: !1,
  touch: !0,
  trigger: "mouseenter focus",
  triggerTarget: null
}, ma, vu), hu = Object.keys(be), gu = function(t) {
  process.env.NODE_ENV !== "production" && va(t, []);
  var n = Object.keys(t);
  n.forEach(function(r) {
    be[r] = t[r];
  });
};
function pa(e) {
  var t = e.plugins || [], n = t.reduce(function(r, i) {
    var a = i.name, l = i.defaultValue;
    if (a) {
      var c;
      r[a] = e[a] !== void 0 ? e[a] : (c = be[a]) != null ? c : l;
    }
    return r;
  }, {});
  return Object.assign({}, e, n);
}
function yu(e, t) {
  var n = t ? Object.keys(pa(Object.assign({}, be, {
    plugins: t
  }))) : hu, r = n.reduce(function(i, a) {
    var l = (e.getAttribute("data-tippy-" + a) || "").trim();
    if (!l)
      return i;
    if (a === "content")
      i[a] = l;
    else
      try {
        i[a] = JSON.parse(l);
      } catch {
        i[a] = l;
      }
    return i;
  }, {});
  return r;
}
function _i(e, t) {
  var n = Object.assign({}, t, {
    content: ua(t.content, [e])
  }, t.ignoreAttributes ? {} : yu(e, t.plugins));
  return n.aria = Object.assign({}, be.aria, n.aria), n.aria = {
    expanded: n.aria.expanded === "auto" ? t.interactive : n.aria.expanded,
    content: n.aria.content === "auto" ? t.interactive ? null : "describedby" : n.aria.content
  }, n;
}
function va(e, t) {
  e === void 0 && (e = {}), t === void 0 && (t = []);
  var n = Object.keys(e);
  n.forEach(function(r) {
    var i = Xl(be, Object.keys(ma)), a = !Kl(i, r);
    a && (a = t.filter(function(l) {
      return l.name === r;
    }).length === 0), Ze(a, ["`" + r + "`", "is not a valid prop. You may have spelled it incorrectly, or if it's", "a plugin, forgot to pass it in an array as props.plugins.", `

`, `All props: https://atomiks.github.io/tippyjs/v6/all-props/
`, "Plugins: https://atomiks.github.io/tippyjs/v6/plugins/"].join(" "));
  });
}
function xi(e) {
  var t = e.firstElementChild, n = Mn(t.children);
  return {
    box: t,
    content: n.find(function(r) {
      return r.classList.contains(Ul);
    }),
    arrow: n.find(function(r) {
      return r.classList.contains(Yl) || r.classList.contains(Gl);
    }),
    backdrop: n.find(function(r) {
      return r.classList.contains(ql);
    })
  };
}
var bu = 1, Tn = [], ar = [];
function _u(e, t) {
  var n = _i(e, Object.assign({}, be, pa(vi(t)))), r, i, a, l = !1, c = !1, d = !1, p = !1, f, h, b, g = [], j = mi(It, n.interactiveDebounce), O, E = bu++, N = null, T = Jl(n.plugins), B = {
    // Is the instance currently enabled?
    isEnabled: !0,
    // Is the tippy currently showing and not transitioning out?
    isVisible: !1,
    // Has the instance been destroyed?
    isDestroyed: !1,
    // Is the tippy currently mounted to the DOM?
    isMounted: !1,
    // Has the tippy finished transitioning in?
    isShown: !1
  }, u = {
    // properties
    id: E,
    reference: e,
    popper: eu(),
    popperInstance: N,
    props: n,
    state: B,
    plugins: T,
    // methods
    clearDelayTimeouts: wt,
    setProps: jt,
    setContent: Mt,
    show: bn,
    hide: _n,
    hideWithInteractivity: Gn,
    enable: _t,
    disable: xt,
    unmount: Kn,
    destroy: Xn
  };
  if (!n.render)
    return process.env.NODE_ENV !== "production" && _r(!0, "render() function has not been supplied."), u;
  var R = n.render(u), _ = R.popper, W = R.onUpdate;
  _.setAttribute("data-tippy-root", ""), _.id = "tippy-" + u.id, u.popper = _, e._tippy = u, _._tippy = u;
  var G = T.map(function(m) {
    return m.fn(u);
  }), A = e.hasAttribute("aria-expanded");
  return at(), Z(), K(), Y("onCreate", [u]), n.showOnCreate && Ge(), _.addEventListener("mouseenter", function() {
    u.props.interactive && u.state.isVisible && u.clearDelayTimeouts();
  }), _.addEventListener("mouseleave", function() {
    u.props.interactive && u.props.trigger.indexOf("mouseenter") >= 0 && P().addEventListener("mousemove", j);
  }), u;
  function L() {
    var m = u.props.touch;
    return Array.isArray(m) ? m : [m, 0];
  }
  function U() {
    return L()[0] === "hold";
  }
  function V() {
    var m;
    return !!((m = u.props.render) != null && m.$$tippy);
  }
  function I() {
    return O || e;
  }
  function P() {
    var m = I().parentNode;
    return m ? au(m) : document;
  }
  function S() {
    return xi(_);
  }
  function X(m) {
    return u.state.isMounted && !u.state.isVisible || We.isTouch || f && f.type === "focus" ? 0 : nr(u.props.delay, m ? 0 : 1, be.delay);
  }
  function K(m) {
    m === void 0 && (m = !1), _.style.pointerEvents = u.props.interactive && !m ? "" : "none", _.style.zIndex = "" + u.props.zIndex;
  }
  function Y(m, x, w) {
    if (w === void 0 && (w = !0), G.forEach(function($) {
      $[m] && $[m].apply($, x);
    }), w) {
      var z;
      (z = u.props)[m].apply(z, x);
    }
  }
  function ee() {
    var m = u.props.aria;
    if (m.content) {
      var x = "aria-" + m.content, w = _.id, z = Vt(u.props.triggerTarget || e);
      z.forEach(function($) {
        var re = $.getAttribute(x);
        if (u.state.isVisible)
          $.setAttribute(x, re ? re + " " + w : w);
        else {
          var ne = re && re.replace(w, "").trim();
          ne ? $.setAttribute(x, ne) : $.removeAttribute(x);
        }
      });
    }
  }
  function Z() {
    if (!(A || !u.props.aria.expanded)) {
      var m = Vt(u.props.triggerTarget || e);
      m.forEach(function(x) {
        u.props.interactive ? x.setAttribute("aria-expanded", u.state.isVisible && x === I() ? "true" : "false") : x.removeAttribute("aria-expanded");
      });
    }
  }
  function Re() {
    P().removeEventListener("mousemove", j), Tn = Tn.filter(function(m) {
      return m !== j;
    });
  }
  function ve(m) {
    if (!(We.isTouch && (d || m.type === "mousedown"))) {
      var x = m.composedPath && m.composedPath()[0] || m.target;
      if (!(u.props.interactive && gi(_, x))) {
        if (Vt(u.props.triggerTarget || e).some(function(w) {
          return gi(w, x);
        })) {
          if (We.isTouch || u.state.isVisible && u.props.trigger.indexOf("click") >= 0)
            return;
        } else
          Y("onClickOutside", [u, m]);
        u.props.hideOnClick === !0 && (u.clearDelayTimeouts(), u.hide(), c = !0, setTimeout(function() {
          c = !1;
        }), u.state.isMounted || ge());
      }
    }
  }
  function Ae() {
    d = !0;
  }
  function ke() {
    d = !1;
  }
  function he() {
    var m = P();
    m.addEventListener("mousedown", ve, !0), m.addEventListener("touchend", ve, Ct), m.addEventListener("touchstart", ke, Ct), m.addEventListener("touchmove", Ae, Ct);
  }
  function ge() {
    var m = P();
    m.removeEventListener("mousedown", ve, !0), m.removeEventListener("touchend", ve, Ct), m.removeEventListener("touchstart", ke, Ct), m.removeEventListener("touchmove", Ae, Ct);
  }
  function Ie(m, x) {
    Me(m, function() {
      !u.state.isVisible && _.parentNode && _.parentNode.contains(_) && x();
    });
  }
  function ue(m, x) {
    Me(m, x);
  }
  function Me(m, x) {
    var w = S().box;
    function z($) {
      $.target === w && (ir(w, "remove", z), x());
    }
    if (m === 0)
      return x();
    ir(w, "remove", h), ir(w, "add", z), h = z;
  }
  function ye(m, x, w) {
    w === void 0 && (w = !1);
    var z = Vt(u.props.triggerTarget || e);
    z.forEach(function($) {
      $.addEventListener(m, x, w), g.push({
        node: $,
        eventType: m,
        handler: x,
        options: w
      });
    });
  }
  function at() {
    U() && (ye("touchstart", yt, {
      passive: !0
    }), ye("touchend", qe, {
      passive: !0
    })), Zl(u.props.trigger).forEach(function(m) {
      if (m !== "manual")
        switch (ye(m, yt), m) {
          case "mouseenter":
            ye("mouseleave", qe);
            break;
          case "focus":
            ye(du ? "focusout" : "blur", Ne);
            break;
          case "focusin":
            ye("focusout", Ne);
            break;
        }
    });
  }
  function gt() {
    g.forEach(function(m) {
      var x = m.node, w = m.eventType, z = m.handler, $ = m.options;
      x.removeEventListener(w, z, $);
    }), g = [];
  }
  function yt(m) {
    var x, w = !1;
    if (!(!u.state.isEnabled || ot(m) || c)) {
      var z = ((x = f) == null ? void 0 : x.type) === "focus";
      f = m, O = m.currentTarget, Z(), !u.state.isVisible && nu(m) && Tn.forEach(function($) {
        return $(m);
      }), m.type === "click" && (u.props.trigger.indexOf("mouseenter") < 0 || l) && u.props.hideOnClick !== !1 && u.state.isVisible ? w = !0 : Ge(m), m.type === "click" && (l = !w), w && !z && Ke(m);
    }
  }
  function It(m) {
    var x = m.target, w = I().contains(x) || _.contains(x);
    if (!(m.type === "mousemove" && w)) {
      var z = Le().concat(_).map(function($) {
        var re, ne = $._tippy, ze = (re = ne.popperInstance) == null ? void 0 : re.state;
        return ze ? {
          popperRect: $.getBoundingClientRect(),
          popperState: ze,
          props: n
        } : null;
      }).filter(Boolean);
      ou(z, m) && (Re(), Ke(m));
    }
  }
  function qe(m) {
    var x = ot(m) || u.props.trigger.indexOf("click") >= 0 && l;
    if (!x) {
      if (u.props.interactive) {
        u.hideWithInteractivity(m);
        return;
      }
      Ke(m);
    }
  }
  function Ne(m) {
    u.props.trigger.indexOf("focusin") < 0 && m.target !== I() || u.props.interactive && m.relatedTarget && _.contains(m.relatedTarget) || Ke(m);
  }
  function ot(m) {
    return We.isTouch ? U() !== m.type.indexOf("touch") >= 0 : !1;
  }
  function bt() {
    Ye();
    var m = u.props, x = m.popperOptions, w = m.placement, z = m.offset, $ = m.getReferenceClientRect, re = m.moveTransition, ne = V() ? xi(_).arrow : null, ze = $ ? {
      getBoundingClientRect: $,
      contextElement: $.contextElement || I()
    } : e, Lt = {
      name: "$$tippy",
      enabled: !0,
      phase: "beforeWrite",
      requires: ["computeStyles"],
      fn: function(Et) {
        var st = Et.state;
        if (V()) {
          var xn = S(), zt = xn.box;
          ["placement", "reference-hidden", "escaped"].forEach(function(Ft) {
            Ft === "placement" ? zt.setAttribute("data-placement", st.placement) : st.attributes.popper["data-popper-" + Ft] ? zt.setAttribute("data-" + Ft, "") : zt.removeAttribute("data-" + Ft);
          }), st.attributes.popper = {};
        }
      }
    }, Fe = [{
      name: "offset",
      options: {
        offset: z
      }
    }, {
      name: "preventOverflow",
      options: {
        padding: {
          top: 2,
          bottom: 2,
          left: 5,
          right: 5
        }
      }
    }, {
      name: "flip",
      options: {
        padding: 5
      }
    }, {
      name: "computeStyles",
      options: {
        adaptive: !re
      }
    }, Lt];
    V() && ne && Fe.push({
      name: "arrow",
      options: {
        element: ne,
        padding: 3
      }
    }), Fe.push.apply(Fe, (x == null ? void 0 : x.modifiers) || []), u.popperInstance = na(ze, _, Object.assign({}, x, {
      placement: w,
      onFirstUpdate: b,
      modifiers: Fe
    }));
  }
  function Ye() {
    u.popperInstance && (u.popperInstance.destroy(), u.popperInstance = null);
  }
  function $e() {
    var m = u.props.appendTo, x, w = I();
    u.props.interactive && m === la || m === "parent" ? x = w.parentNode : x = ua(m, [w]), x.contains(_) || x.appendChild(_), u.state.isMounted = !0, bt(), process.env.NODE_ENV !== "production" && Ze(u.props.interactive && m === be.appendTo && w.nextElementSibling !== _, ["Interactive tippy element may not be accessible via keyboard", "navigation because it is not directly after the reference element", "in the DOM source order.", `

`, "Using a wrapper <div> or <span> tag around the reference element", "solves this by creating a new parentNode context.", `

`, "Specifying `appendTo: document.body` silences this warning, but it", "assumes you are using a focus management solution to handle", "keyboard navigation.", `

`, "See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity"].join(" "));
  }
  function Le() {
    return Mn(_.querySelectorAll("[data-tippy-root]"));
  }
  function Ge(m) {
    u.clearDelayTimeouts(), m && Y("onTrigger", [u, m]), he();
    var x = X(!0), w = L(), z = w[0], $ = w[1];
    We.isTouch && z === "hold" && $ && (x = $), x ? r = setTimeout(function() {
      u.show();
    }, x) : u.show();
  }
  function Ke(m) {
    if (u.clearDelayTimeouts(), Y("onUntrigger", [u, m]), !u.state.isVisible) {
      ge();
      return;
    }
    if (!(u.props.trigger.indexOf("mouseenter") >= 0 && u.props.trigger.indexOf("click") >= 0 && ["mouseleave", "mousemove"].indexOf(m.type) >= 0 && l)) {
      var x = X(!1);
      x ? i = setTimeout(function() {
        u.state.isVisible && u.hide();
      }, x) : a = requestAnimationFrame(function() {
        u.hide();
      });
    }
  }
  function _t() {
    u.state.isEnabled = !0;
  }
  function xt() {
    u.hide(), u.state.isEnabled = !1;
  }
  function wt() {
    clearTimeout(r), clearTimeout(i), cancelAnimationFrame(a);
  }
  function jt(m) {
    if (process.env.NODE_ENV !== "production" && Ze(u.state.isDestroyed, Bt("setProps")), !u.state.isDestroyed) {
      Y("onBeforeUpdate", [u, m]), gt();
      var x = u.props, w = _i(e, Object.assign({}, x, vi(m), {
        ignoreAttributes: !0
      }));
      u.props = w, at(), x.interactiveDebounce !== w.interactiveDebounce && (Re(), j = mi(It, w.interactiveDebounce)), x.triggerTarget && !w.triggerTarget ? Vt(x.triggerTarget).forEach(function(z) {
        z.removeAttribute("aria-expanded");
      }) : w.triggerTarget && e.removeAttribute("aria-expanded"), Z(), K(), W && W(x, w), u.popperInstance && (bt(), Le().forEach(function(z) {
        requestAnimationFrame(z._tippy.popperInstance.forceUpdate);
      })), Y("onAfterUpdate", [u, m]);
    }
  }
  function Mt(m) {
    u.setProps({
      content: m
    });
  }
  function bn() {
    process.env.NODE_ENV !== "production" && Ze(u.state.isDestroyed, Bt("show"));
    var m = u.state.isVisible, x = u.state.isDestroyed, w = !u.state.isEnabled, z = We.isTouch && !u.props.touch, $ = nr(u.props.duration, 0, be.duration);
    if (!(m || x || w || z) && !I().hasAttribute("disabled") && (Y("onShow", [u], !1), u.props.onShow(u) !== !1)) {
      if (u.state.isVisible = !0, V() && (_.style.visibility = "visible"), K(), he(), u.state.isMounted || (_.style.transition = "none"), V()) {
        var re = S(), ne = re.box, ze = re.content;
        rr([ne, ze], 0);
      }
      b = function() {
        var Fe;
        if (!(!u.state.isVisible || p)) {
          if (p = !0, _.offsetHeight, _.style.transition = u.props.moveTransition, V() && u.props.animation) {
            var Xt = S(), Et = Xt.box, st = Xt.content;
            rr([Et, st], $), hi([Et, st], "visible");
          }
          ee(), Z(), pi(ar, u), (Fe = u.popperInstance) == null || Fe.forceUpdate(), Y("onMount", [u]), u.props.animation && V() && ue($, function() {
            u.state.isShown = !0, Y("onShown", [u]);
          });
        }
      }, $e();
    }
  }
  function _n() {
    process.env.NODE_ENV !== "production" && Ze(u.state.isDestroyed, Bt("hide"));
    var m = !u.state.isVisible, x = u.state.isDestroyed, w = !u.state.isEnabled, z = nr(u.props.duration, 1, be.duration);
    if (!(m || x || w) && (Y("onHide", [u], !1), u.props.onHide(u) !== !1)) {
      if (u.state.isVisible = !1, u.state.isShown = !1, p = !1, l = !1, V() && (_.style.visibility = "hidden"), Re(), ge(), K(!0), V()) {
        var $ = S(), re = $.box, ne = $.content;
        u.props.animation && (rr([re, ne], z), hi([re, ne], "hidden"));
      }
      ee(), Z(), u.props.animation ? V() && Ie(z, u.unmount) : u.unmount();
    }
  }
  function Gn(m) {
    process.env.NODE_ENV !== "production" && Ze(u.state.isDestroyed, Bt("hideWithInteractivity")), P().addEventListener("mousemove", j), pi(Tn, j), j(m);
  }
  function Kn() {
    process.env.NODE_ENV !== "production" && Ze(u.state.isDestroyed, Bt("unmount")), u.state.isVisible && u.hide(), u.state.isMounted && (Ye(), Le().forEach(function(m) {
      m._tippy.unmount();
    }), _.parentNode && _.parentNode.removeChild(_), ar = ar.filter(function(m) {
      return m !== u;
    }), u.state.isMounted = !1, Y("onHidden", [u]));
  }
  function Xn() {
    process.env.NODE_ENV !== "production" && Ze(u.state.isDestroyed, Bt("destroy")), !u.state.isDestroyed && (u.clearDelayTimeouts(), u.unmount(), gt(), delete e._tippy, u.state.isDestroyed = !0, Y("onDestroy", [u]));
  }
}
function gn(e, t) {
  t === void 0 && (t = {});
  var n = be.plugins.concat(t.plugins || []);
  process.env.NODE_ENV !== "production" && (pu(e), va(t, n)), lu();
  var r = Object.assign({}, t, {
    plugins: n
  }), i = iu(e);
  if (process.env.NODE_ENV !== "production") {
    var a = br(r.content), l = i.length > 1;
    Ze(a && l, ["tippy() was passed an Element as the `content` prop, but more than", "one tippy instance was created by this invocation. This means the", "content element will only be appended to the last tippy instance.", `

`, "Instead, pass the .innerHTML of the element, or use a function that", "returns a cloned version of the element instead.", `

`, `1) content: element.innerHTML
`, "2) content: () => element.cloneNode(true)"].join(" "));
  }
  var c = i.reduce(function(d, p) {
    var f = p && _u(p, r);
    return f && d.push(f), d;
  }, []);
  return br(e) ? c[0] : c;
}
gn.defaultProps = be;
gn.setDefaultProps = gu;
gn.currentInput = We;
Object.assign({}, Gi, {
  effect: function(t) {
    var n = t.state, r = {
      popper: {
        position: n.options.strategy,
        left: "0",
        top: "0",
        margin: "0"
      },
      arrow: {
        position: "absolute"
      },
      reference: {}
    };
    Object.assign(n.elements.popper.style, r.popper), n.styles = r, n.elements.arrow && Object.assign(n.elements.arrow.style, r.arrow);
  }
});
gn.setDefaultProps({
  animation: !1
});
function ha(e, t) {
  if (e == null) return {};
  var n = {}, r = Object.keys(e), i, a;
  for (a = 0; a < r.length; a++)
    i = r[a], !(t.indexOf(i) >= 0) && (n[i] = e[i]);
  return n;
}
var ga = typeof window < "u" && typeof document < "u";
function xr(e, t) {
  e && (typeof e == "function" && e(t), {}.hasOwnProperty.call(e, "current") && (e.current = t));
}
function wi() {
  return ga && document.createElement("div");
}
function xu(e) {
  var t = {
    "data-placement": e.placement
  };
  return e.referenceHidden && (t["data-reference-hidden"] = ""), e.escaped && (t["data-escaped"] = ""), t;
}
function ya(e, t) {
  if (e === t)
    return !0;
  if (typeof e == "object" && e != null && typeof t == "object" && t != null) {
    if (Object.keys(e).length !== Object.keys(t).length)
      return !1;
    for (var n in e)
      if (t.hasOwnProperty(n)) {
        if (!ya(e[n], t[n]))
          return !1;
      } else
        return !1;
    return !0;
  } else
    return !1;
}
function wu(e) {
  var t = [];
  return e.forEach(function(n) {
    t.find(function(r) {
      return ya(n, r);
    }) || t.push(n);
  }), t;
}
function ju(e, t) {
  var n, r;
  return Object.assign({}, t, {
    popperOptions: Object.assign({}, e.popperOptions, t.popperOptions, {
      modifiers: wu([].concat(((n = e.popperOptions) == null ? void 0 : n.modifiers) || [], ((r = t.popperOptions) == null ? void 0 : r.modifiers) || []))
    })
  });
}
var or = ga ? Ha : et;
function Eu(e) {
  var t = Di();
  return t.current || (t.current = typeof e == "function" ? e() : e), t.current;
}
function ji(e, t, n) {
  n.split(/\s+/).forEach(function(r) {
    r && e.classList[t](r);
  });
}
var Ou = {
  name: "className",
  defaultValue: "",
  fn: function(t) {
    var n = t.popper.firstElementChild, r = function() {
      var c;
      return !!((c = t.props.render) != null && c.$$tippy);
    };
    function i() {
      if (t.props.className && !r()) {
        process.env.NODE_ENV !== "production" && console.warn(["@tippyjs/react: Cannot use `className` prop in conjunction with", "`render` prop. Place the className on the element you are", "rendering."].join(" "));
        return;
      }
      ji(n, "add", t.props.className);
    }
    function a() {
      r() && ji(n, "remove", t.props.className);
    }
    return {
      onCreate: i,
      onBeforeUpdate: a,
      onAfterUpdate: i
    };
  }
};
function Cu(e) {
  function t(n) {
    var r = n.children, i = n.content, a = n.visible, l = n.singleton, c = n.render, d = n.reference, p = n.disabled, f = p === void 0 ? !1 : p, h = n.ignoreAttributes, b = h === void 0 ? !0 : h;
    n.__source, n.__self;
    var g = ha(n, ["children", "content", "visible", "singleton", "render", "reference", "disabled", "ignoreAttributes", "__source", "__self"]), j = a !== void 0, O = l !== void 0, E = Pe(!1), N = E[0], T = E[1], B = Pe({}), u = B[0], R = B[1], _ = Pe(), W = _[0], G = _[1], A = Eu(function() {
      return {
        container: wi(),
        renders: 1
      };
    }), L = Object.assign({
      ignoreAttributes: b
    }, g, {
      content: A.container
    });
    j && (process.env.NODE_ENV !== "production" && ["trigger", "hideOnClick", "showOnCreate"].forEach(function(P) {
      L[P] !== void 0 && console.warn(["@tippyjs/react: Cannot specify `" + P + "` prop in", "controlled mode (`visible` prop)"].join(" "));
    }), L.trigger = "manual", L.hideOnClick = !1), O && (f = !0);
    var U = L, V = L.plugins || [];
    c && (U = Object.assign({}, L, {
      plugins: O && l.data != null ? [].concat(V, [{
        fn: function() {
          return {
            onTrigger: function(X, K) {
              var Y = l.data.children.find(function(ee) {
                var Z = ee.instance;
                return Z.reference === K.currentTarget;
              });
              X.state.$$activeSingletonInstance = Y.instance, G(Y.content);
            }
          };
        }
      }]) : V,
      render: function() {
        return {
          popper: A.container
        };
      }
    }));
    var I = [d].concat(r ? [r.type] : []);
    return or(function() {
      var P = d;
      d && d.hasOwnProperty("current") && (P = d.current);
      var S = e(P || A.ref || wi(), Object.assign({}, U, {
        plugins: [Ou].concat(L.plugins || [])
      }));
      return A.instance = S, f && S.disable(), a && S.show(), O && l.hook({
        instance: S,
        content: i,
        props: U,
        setSingletonContent: G
      }), T(!0), function() {
        S.destroy(), l == null || l.cleanup(S);
      };
    }, I), or(function() {
      var P;
      if (A.renders === 1) {
        A.renders++;
        return;
      }
      var S = A.instance;
      S.setProps(ju(S.props, U)), (P = S.popperInstance) == null || P.forceUpdate(), f ? S.disable() : S.enable(), j && (a ? S.show() : S.hide()), O && l.hook({
        instance: S,
        content: i,
        props: U,
        setSingletonContent: G
      });
    }), or(function() {
      var P;
      if (c) {
        var S = A.instance;
        S.setProps({
          popperOptions: Object.assign({}, S.props.popperOptions, {
            modifiers: [].concat((((P = S.props.popperOptions) == null ? void 0 : P.modifiers) || []).filter(function(X) {
              var K = X.name;
              return K !== "$$tippyReact";
            }), [{
              name: "$$tippyReact",
              enabled: !0,
              phase: "beforeWrite",
              requires: ["computeStyles"],
              fn: function(K) {
                var Y, ee = K.state, Z = (Y = ee.modifiersData) == null ? void 0 : Y.hide;
                (u.placement !== ee.placement || u.referenceHidden !== (Z == null ? void 0 : Z.isReferenceHidden) || u.escaped !== (Z == null ? void 0 : Z.hasPopperEscaped)) && R({
                  placement: ee.placement,
                  referenceHidden: Z == null ? void 0 : Z.isReferenceHidden,
                  escaped: Z == null ? void 0 : Z.hasPopperEscaped
                }), ee.attributes.popper = {};
              }
            }])
          })
        });
      }
    }, [u.placement, u.referenceHidden, u.escaped].concat(I)), /* @__PURE__ */ le.createElement(le.Fragment, null, r ? /* @__PURE__ */ Pi(r, {
      ref: function(S) {
        A.ref = S, xr(r.ref, S);
      }
    }) : null, N && /* @__PURE__ */ Ai(c ? c(xu(u), W, A.instance) : i, A.container));
  }
  return t;
}
var Tu = function(e, t) {
  return /* @__PURE__ */ $i(function(r, i) {
    var a = r.children, l = ha(r, ["children"]);
    return (
      // If I spread them separately here, Babel adds the _extends ponyfill for
      // some reason
      /* @__PURE__ */ le.createElement(e, Object.assign({}, t, l), a ? /* @__PURE__ */ Pi(a, {
        ref: function(d) {
          xr(i, d), xr(a.ref, d);
        }
      }) : null)
    );
  });
}, Su = /* @__PURE__ */ Tu(/* @__PURE__ */ Cu(gn), {
  render: function() {
    return "";
  }
});
const ku = "_content_1edfq_1", Nu = "_arrow_1edfq_10", $u = "_arrowWrapper_1edfq_18", sr = {
  content: ku,
  arrow: Nu,
  arrowWrapper: $u
}, Pu = (e) => /* @__PURE__ */ s.jsx("div", { className: "dark", children: /* @__PURE__ */ s.jsxs(
  "div",
  {
    className: [sr.content, Gt.boxStrong, H.radius].join(" "),
    tabIndex: -1,
    ...e.attrs,
    children: [
      e.attrs && /* @__PURE__ */ s.jsx("div", { "data-popper-arrow": !0, className: sr.arrowWrapper, children: /* @__PURE__ */ s.jsx("div", { className: [sr.arrow].join(" ") }) }),
      /* @__PURE__ */ s.jsx(aa, { children: e.children })
    ]
  }
) }), Du = (e) => /* @__PURE__ */ s.jsx(
  Su,
  {
    children: e.children,
    placement: e.placement ?? "top",
    render: (t) => /* @__PURE__ */ s.jsx(Pu, { children: e.content, attrs: t })
  }
), Ru = "_container_rqnlg_1", Au = "_containerFill_rqnlg_5", Iu = "_childFix_rqnlg_9", Mu = "_childFill_rqnlg_13", Sn = {
  container: Ru,
  containerFill: Au,
  childFix: Iu,
  childFill: Mu
}, Lu = (e) => (t) => t.element ? t : { element: t, fill: e }, zu = [k, ia, De, Du, Se], Fn = (e) => /* @__PURE__ */ s.jsx("div", { className: [Sn.container, e.fill ? Sn.containerFill : ""].join(" "), children: e.children.map(Lu(e.fill)).map((t, n) => {
  if (zu.includes(t.element.type) === !1 && !e.skipChildTypeCheck)
    throw Error(`Unsupported child type: ${t.element.type}`);
  const r = [];
  return n !== 0 && r.push("group-tail"), n !== e.children.length - 1 && r.push("group-init"), r.push(t.fill ? Sn.childFill : Sn.childFix), /* @__PURE__ */ s.jsx("div", { className: r.join(" "), children: t.element }, n);
}) }), Fu = "_gray_1bqxl_1", Wu = "_red_1bqxl_6", Bu = "_yellow_1bqxl_11", Vu = "_green_1bqxl_16", Hu = "_blue_1bqxl_21", Uu = "_indigo_1bqxl_26", qu = "_purple_1bqxl_31", Yu = "_pink_1bqxl_36", ct = {
  gray: Fu,
  red: Wu,
  yellow: Bu,
  green: Vu,
  blue: Hu,
  indigo: Uu,
  purple: qu,
  pink: Yu
}, ba = {
  red: ct.red,
  yellow: ct.yellow,
  green: ct.green,
  blue: ct.blue,
  indigo: ct.indigo,
  purple: ct.purple,
  pink: ct.pink,
  gray: ct.gray
}, Lp = Object.keys(ba), Gu = "_input_15mbs_1", Ku = "_check_15mbs_1", Xu = "_indeterminate_15mbs_2", cr = {
  input: Gu,
  check: Ku,
  indeterminate: Xu
}, Zu = "_container_1q0sq_1", Ju = "_input_1q0sq_9", Qu = "_icon_1q0sq_23", ed = "_label_1q0sq_37", Qe = {
  container: Zu,
  input: Ju,
  icon: Qu,
  label: ed
}, td = "_input_19am0_1", nd = "_icon_19am0_83", rd = "_label_19am0_112", lr = {
  input: td,
  icon: nd,
  label: rd
}, Wn = (e) => {
  const t = le.useRef(null), n = e.forwardedRef;
  et(() => {
    if (t.current === null) throw Error("Ref is null");
    if (n)
      switch (typeof n) {
        case "function":
          n(t.current);
          break;
        case "object":
          n.current = t.current;
          break;
        default:
          throw Error(`Unknown props.forwardedRef type: ${typeof n}`);
      }
  }, [n]), et(() => {
    if (t.current === null) throw Error("Ref is null");
    e.indeterminate !== void 0 && (t.current.indeterminate = e.indeterminate);
  }, [e.indeterminate]);
  const r = Wn.styles.outset;
  return /* @__PURE__ */ s.jsxs("label", { className: Qe.container, children: [
    /* @__PURE__ */ s.jsx(
      "input",
      {
        type: "checkbox",
        className: [
          Qe.input,
          r.input,
          cr.input,
          H.radius,
          Rt.normal
        ].join(" "),
        disabled: e.disabled,
        defaultChecked: e.defaultChecked,
        ref: t,
        checked: e.checked,
        onChange: (i) => {
          var a;
          return (a = e.setChecked) == null ? void 0 : a.call(e, i.target.checked);
        }
      }
    ),
    /* @__PURE__ */ s.jsx(
      "span",
      {
        className: [Qe.icon, r.icon, cr.check].join(" "),
        children: /* @__PURE__ */ s.jsx(mt, { display: "block", component: ae.check })
      }
    ),
    /* @__PURE__ */ s.jsx(
      "span",
      {
        className: [Qe.icon, r.icon, cr.indeterminate].join(" "),
        children: /* @__PURE__ */ s.jsx(mt, { display: "block", component: ae.dash })
      }
    ),
    /* @__PURE__ */ s.jsx(
      "span",
      {
        className: [
          Qe.label,
          r.label,
          e.hideLabel ? ra.srOnly : ""
        ].join(" "),
        children: e.children
      }
    )
  ] });
};
Wn.styles = {
  outset: {
    input: lr.input,
    icon: lr.icon,
    label: lr.label
  }
};
const Lr = (e) => (t) => {
  if (typeof t != "string") return;
  const n = t.split(/[./ _-]+/);
  if (n.length !== 3) return;
  const [r, i, a] = e(n), l = parseInt(r, 10), c = parseInt(i, 10) - 1, d = parseInt(a, 10);
  if (isNaN(l) || String(l).length > 4 || isNaN(d) || d <= 0 || d > 31 || isNaN(c) || c < 0 || c >= 12) return;
  const p = new Date(l, c, d, 12, 0, 0, 0);
  return p.setFullYear(l), p;
}, zr = (e) => {
  const t = e.getFullYear(), n = e.getMonth() + 1, r = e.getDate();
  return [t.toString(), n.toString(), r.toString()];
}, id = {
  placeholder: "yyyy/mm/dd",
  parse: Lr((e) => {
    const [t, n, r] = e;
    return [t, n, r];
  }),
  format: (e) => {
    const [t, n, r] = zr(e);
    return `${t}/${n}/${r}`;
  }
}, ad = {
  placeholder: "dd/mm/yyyy",
  parse: Lr((e) => {
    const [t, n, r] = e;
    return [r, n, t];
  }),
  format: (e) => {
    const [t, n, r] = zr(e);
    return `${r}/${n}/${t}`;
  }
}, od = {
  placeholder: "mm/dd/yyyy",
  parse: Lr((e) => {
    const [t, n, r] = e;
    return [r, t, n];
  }),
  format: (e) => {
    const [t, n, r] = zr(e);
    return `${n}/${r}/${t}`;
  }
}, sd = { dmy: ad, mdy: od, ymd: id }, Fr = () => /* @__PURE__ */ s.jsx("div", { style: { color: "red" }, children: "TODO" });
Fr.formats = sd;
Fr.sizes = Se.sizes;
Fr.styles = Se.styles;
const cd = "_fill_1j275_1", ld = "_container_1j275_8", ud = "_backdrop_1j275_17", dd = "_dialog_1j275_22", fd = "_widthFixed_1j275_44", St = {
  fill: cd,
  container: ld,
  backdrop: ud,
  dialog: dd,
  widthFixed: fd
}, Bn = (e) => {
  const t = e.width === "content" ? St.widthAuto : St.widthFixed, n = Bn.styles.outset;
  return /* @__PURE__ */ s.jsx(
    "div",
    {
      className: [St.dialog, n, t].join(" "),
      children: e.children
    }
  );
};
Bn.styles = {
  outset: [H.px1, H.strong, Gt.boxStrong, _e.strong].join(
    " "
  )
};
const Vn = (e) => /* @__PURE__ */ s.jsxs(
  "div",
  {
    className: [St.container, St.fill].join(" "),
    onKeyDown: (t) => {
      var n;
      t.key === "Escape" && ((n = e.onEsc) == null || n.call(e));
    },
    children: [
      /* @__PURE__ */ s.jsx(
        "div",
        {
          className: [_e.weak, St.backdrop, St.fill].join(" "),
          onClick: e.onEsc
        }
      ),
      /* @__PURE__ */ s.jsx(Bn, { ...e })
    ]
  }
), md = "_header_5lpzd_1", pd = "_body_5lpzd_7", vd = "_footer_5lpzd_11", Wr = {
  header: md,
  body: pd,
  footer: vd
}, Hn = (e) => /* @__PURE__ */ s.jsx("div", { className: Wr.body, children: e.children }), hd = (e) => /* @__PURE__ */ s.jsx(
  "div",
  {
    className: [Wr.header, H.weak].join(" "),
    children: e.children
  }
), gd = (e) => /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
  /* @__PURE__ */ s.jsx(
    "div",
    {
      className: [rt.big, rt.strong].join(" "),
      children: e.children
    }
  ),
  /* @__PURE__ */ s.jsx(xe, { size: 16 })
] }), Un = (e) => /* @__PURE__ */ s.jsx("div", { className: Wr.footer, children: e.children }), yd = (e) => {
  if (kr.unmountComponentAtNode(e) === !1) throw Error("No component to unmount");
  e.remove();
}, Br = (e) => {
  const t = document.createElement("div");
  document.body.appendChild(t);
  const n = e(() => yd(t));
  kr.render(n, t);
}, bd = (e) => /* @__PURE__ */ s.jsxs(Vn, { onEsc: e.onOk, width: e.width, children: [
  /* @__PURE__ */ s.jsx(Hn, { children: e.children }),
  /* @__PURE__ */ s.jsx(Un, { children: /* @__PURE__ */ s.jsx(k, { minWidth: !0, autoFocus: !0, highlight: !0, onClick: e.onOk, children: "OK" }) })
] }), _d = (e, t) => new Promise((n) => {
  Br((r) => /* @__PURE__ */ s.jsx(
    bd,
    {
      onOk: () => {
        n(), r();
      },
      children: e,
      width: t == null ? void 0 : t.width
    }
  ));
}), xd = (e) => /* @__PURE__ */ s.jsxs(Vn, { onEsc: e.onCancel, width: e.width, children: [
  /* @__PURE__ */ s.jsx(Hn, { children: e.children }),
  /* @__PURE__ */ s.jsxs(Un, { children: [
    /* @__PURE__ */ s.jsx(k, { minWidth: !0, onClick: e.onCancel, children: "Cancel" }),
    /* @__PURE__ */ s.jsx(k, { minWidth: !0, autoFocus: !0, highlight: !0, onClick: e.onOk, children: "OK" })
  ] })
] }), wd = (e, t) => new Promise((n) => {
  Br((r) => /* @__PURE__ */ s.jsx(
    xd,
    {
      onCancel: () => {
        n(!1), r();
      },
      onOk: () => {
        n(!0), r();
      },
      children: e,
      width: t == null ? void 0 : t.width
    }
  ));
}), jd = "_container_avbge_1", Ed = "_medium_avbge_8", Od = "_small_avbge_13", wr = {
  container: jd,
  medium: Ed,
  small: Od
}, Cd = (e) => {
  const t = e.style ?? mn.styles.outset, n = e.size ?? mn.sizes.medium;
  return [wr.container, Rt.normal, t.main, n.main].join(" ");
}, Td = (e, t) => {
  const n = oa(e, [
    "className",
    "style",
    "size",
    "defaultValue",
    "value",
    "setValue",
    "onChange"
  ]);
  return /* @__PURE__ */ s.jsx(
    "textarea",
    {
      ...n,
      ref: t,
      defaultValue: e.defaultValue,
      value: e.value,
      onChange: (r) => {
        var i, a;
        (i = e.onChange) == null || i.call(e, r), (a = e.setValue) == null || a.call(e, r.currentTarget.value);
      },
      className: Cd(e)
    }
  );
}, mn = le.forwardRef(Td);
mn.styles = {
  outset: Se.styles.outset,
  flat: Se.styles.flat
};
mn.sizes = {
  medium: { main: wr.medium },
  small: { main: wr.small }
};
const Sd = (e) => {
  const [t, n] = Pe(e.initialText ?? ""), r = {
    value: t,
    setValue: n,
    autoFocus: !0
  }, i = e.rows ?? 1;
  return /* @__PURE__ */ s.jsx(Vn, { onEsc: e.onCancel, width: e.width, children: /* @__PURE__ */ s.jsxs("form", { onSubmit: () => e.onOk(t), children: [
    /* @__PURE__ */ s.jsxs(Hn, { children: [
      e.children,
      /* @__PURE__ */ s.jsx(xe, { size: 16 }),
      i !== 1 ? /* @__PURE__ */ s.jsx(mn, { ...r, rows: i }) : /* @__PURE__ */ s.jsx(Se, { ...r })
    ] }),
    /* @__PURE__ */ s.jsxs(Un, { children: [
      /* @__PURE__ */ s.jsx(k, { minWidth: !0, onClick: e.onCancel, children: "Cancel" }),
      /* @__PURE__ */ s.jsx(k, { minWidth: !0, type: "submit", highlight: !0, children: "OK" })
    ] })
  ] }) });
}, kd = (e, t, n) => new Promise((r) => {
  Br((i) => /* @__PURE__ */ s.jsx(
    Sd,
    {
      onCancel: () => {
        r(null), i();
      },
      onOk: (a) => {
        r(a), i();
      },
      children: e,
      width: n == null ? void 0 : n.width,
      rows: n == null ? void 0 : n.rows,
      initialText: t
    }
  ));
}), Ue = (e) => (
  // DialogMain is extracted to its own folder to avoid circular dependencies
  /* @__PURE__ */ s.jsx(Vn, { ...e })
);
Ue.Body = Hn;
Ue.Footer = Un;
Ue.Title = gd;
Ue.alert = _d;
Ue.confirm = wd;
Ue.prompt = kd;
Ue.Header = hd;
Ue.Pane = Bn;
const zp = ({ children: e }) => e ? /* @__PURE__ */ s.jsx("div", { className: rt.failureStrong, children: e }) : null, Nd = "_container_d1ojh_1", $d = "_label_d1ojh_6", Pd = "_input_d1ojh_12", ur = {
  container: Nd,
  label: $d,
  input: Pd
}, Fp = (e) => {
  const { label: t, children: n, useLabelTag: r, labelWidth: i } = e, a = { width: i ?? "auto" };
  return Ri(
    r ? "label" : "div",
    { className: ur.container },
    /* @__PURE__ */ s.jsx("span", { className: ur.label, style: a, children: t }),
    /* @__PURE__ */ s.jsx(xe, { size: 8 }),
    /* @__PURE__ */ s.jsx("span", { className: ur.input, children: n })
  );
}, _a = (e) => {
  const { onEsc: t } = e, n = J.useRef(null);
  J.useEffect(() => {
    if (t === void 0) return;
    const i = (a) => {
      const l = n.current;
      a.target instanceof Node && (l != null && l.contains(a.target) || t());
    };
    return document.addEventListener("click", i), () => document.removeEventListener("click", i);
  }, [t]);
  const r = _a.styles.outset;
  return /* @__PURE__ */ s.jsxs("div", { className: r, ref: n, children: [
    /* @__PURE__ */ s.jsx(xe, { size: 8 }),
    e.items.map((i, a) => /* @__PURE__ */ s.jsx(Ui, { item: i }, a)),
    /* @__PURE__ */ s.jsx(xe, { size: 8 })
  ] });
};
_a.styles = {
  outset: [H.px1, H.strong, Gt.boxStrong, _e.strong].join(
    " "
  )
};
const Dd = "_container_168u6_1", Rd = {
  container: Dd
}, Ad = (e) => {
  const [t, n] = Pe(e.value.toString()), r = /* @__PURE__ */ s.jsx(Se, { autoFocus: !0, value: t, setValue: n }), i = /* @__PURE__ */ s.jsx(k, { type: "submit", children: "Go" }), a = (l) => {
    l.preventDefault();
    const c = parseInt(t);
    if (Number.isNaN(c)) {
      Ue.alert("Please enter a valid number");
      return;
    } else
      e.setValue(c);
  };
  return /* @__PURE__ */ s.jsx("form", { className: Rd.container, onSubmit: a, children: /* @__PURE__ */ s.jsx(
    Fn,
    {
      children: [
        { fill: !0, element: r },
        { fill: !1, element: i }
      ]
    }
  ) });
}, Id = "_container_nckvt_1", Md = {
  container: Id
}, Ld = (e, t) => `Please enter a number between ${e} and ${t}`, Wp = (e) => {
  const { setValue: t, min: n, max: r } = e, [i, a] = Pe(!1), l = cn(
    async (c) => {
      if (c < n || c > r) {
        await Ue.alert(Ld(n, r));
        return;
      }
      a(!0), await t(c), a(!1);
    },
    [t, r, n]
  );
  return /* @__PURE__ */ s.jsx("div", { className: Md.container, children: /* @__PURE__ */ s.jsxs(Fn, { skipChildTypeCheck: !0, children: [
    /* @__PURE__ */ s.jsx(
      k,
      {
        icon: ae.chevronLeft,
        iconLabel: "Previous",
        disabled: e.value === n || i,
        onClick: () => l(e.value - 1)
      }
    ),
    /* @__PURE__ */ s.jsx(
      hn,
      {
        content: (c) => /* @__PURE__ */ s.jsx(
          Ad,
          {
            value: e.value,
            setValue: (d) => {
              l(d), c.close();
            }
          }
        ),
        target: (c) => /* @__PURE__ */ s.jsxs(
          k,
          {
            busy: i,
            icon: ae.kebab,
            iconRight: !0,
            selected: c.opened,
            onClick: c.toggle,
            children: [
              e.value,
              /* @__PURE__ */ s.jsx("span", { children: "/" }),
              e.max
            ]
          }
        )
      }
    ),
    /* @__PURE__ */ s.jsx(
      k,
      {
        icon: ae.chevronRight,
        iconLabel: "Next",
        disabled: e.value === r || i,
        onClick: () => l(e.value + 1)
      }
    )
  ] }) });
}, zd = "_contentWidth_1rpu8_6", Fd = "_padding_1rpu8_10", Wd = "_fullHeight_1rpu8_14", dr = {
  contentWidth: zd,
  padding: Fd,
  fullHeight: Wd
}, Vr = (e) => /* @__PURE__ */ s.jsx(
  "div",
  {
    className: [
      Vr.styles.outset,
      e.noPadding ? "" : dr.padding,
      e.contentWidth ? dr.contentWidth : "",
      e.fullHeight ? dr.fullHeight : ""
    ].join(" "),
    children: e.children
  }
);
Vr.styles = {
  outset: [H.px1, _e.strong, H.strong, Gt.boxWeak].join(
    " "
  )
};
const Bd = "_input_1u3mh_1", Vd = "_icon_1u3mh_5", Ei = {
  input: Bd,
  icon: Vd
}, xa = (e) => {
  const t = Wn.styles.outset;
  return /* @__PURE__ */ s.jsxs("label", { className: Qe.container, children: [
    /* @__PURE__ */ s.jsx(
      "input",
      {
        type: "radio",
        className: [Qe.input, Ei.input, t.input, Rt.normal].join(
          " "
        ),
        name: e.name,
        value: e.value,
        checked: e.checked,
        onChange: (n) => {
          var r;
          return (r = e.setValue) == null ? void 0 : r.call(e, n.target.value);
        },
        disabled: e.disabled,
        defaultChecked: e.defaultChecked,
        ref: e.forwardedRef
      }
    ),
    /* @__PURE__ */ s.jsx("span", { className: [Qe.icon, t.icon, Ei.icon].join(" "), children: /* @__PURE__ */ s.jsx(mt, { display: "block", component: ae.dot }) }),
    /* @__PURE__ */ s.jsx(
      "span",
      {
        className: [
          Qe.label,
          t.label,
          e.hideLabel ? ra.srOnly : ""
        ].join(" "),
        children: e.children
      }
    )
  ] });
}, Hd = "_hor_4ed8z_1", Ud = "_container_4ed8z_1", qd = "_item_4ed8z_6", Yd = "_ver_4ed8z_10", kn = {
  hor: Hd,
  container: Ud,
  item: qd,
  ver: Yd
}, Gd = ({ name: e, value: t, setValue: n, option: r }) => /* @__PURE__ */ s.jsx(
  xa,
  {
    name: e,
    checked: t === r.value,
    children: r.label,
    value: r.id,
    setValue: () => n(r.value)
  }
), Kd = (e) => /* @__PURE__ */ s.jsx("div", { className: [kn.container, e.row === !0 ? kn.hor : kn.ver].join(" "), children: e.options.map((t) => /* @__PURE__ */ s.jsx("div", { className: kn.item, children: /* @__PURE__ */ s.jsx(Gd, { ...e, option: t }) }, t.id)) });
Kd.toStringOption = (e) => ({
  id: e,
  value: e,
  label: e
});
const Xd = "_custom_nginr_2", Zd = {
  custom: Xd
}, { custom: Jd } = Zd, Bp = { custom: Jd }, Qd = "_wrapper_1luvl_1", ef = "_container_1luvl_5", tf = "_step_1luvl_12", nf = "_icon_1luvl_17", rf = "_done_1luvl_29", af = "_notDone_1luvl_34", of = "_divider_1luvl_42", sf = "_title_1luvl_50", cf = "_current_1luvl_50", Je = {
  wrapper: Qd,
  container: ef,
  step: tf,
  icon: nf,
  done: rf,
  notDone: af,
  divider: of,
  title: sf,
  current: cf
}, lf = (e) => /* @__PURE__ */ s.jsxs("div", { className: Je.step, children: [
  /* @__PURE__ */ s.jsx(
    "div",
    {
      className: [
        Je.icon,
        e.index <= e.current ? Je.done : [Je.notDone, rt.muted].join(" ")
      ].join(" "),
      children: e.index < e.current ? /* @__PURE__ */ s.jsx(mt, { component: ae.check, size: 16, display: "block" }) : /* @__PURE__ */ s.jsx("span", { children: e.index + 1 })
    }
  ),
  /* @__PURE__ */ s.jsx(
    "span",
    {
      className: [
        Je.title,
        e.index === e.current ? Je.current : "",
        e.index > e.current ? rt.muted : ""
      ].join(" "),
      children: e.step.title
    }
  )
] }), uf = () => /* @__PURE__ */ s.jsx("div", { className: [Je.divider, H.strong].join(" ") }), Vp = (e) => {
  const t = [];
  return e.steps.forEach((n, r) => {
    t.push(
      /* @__PURE__ */ s.jsx(
        lf,
        {
          step: n,
          index: r,
          current: e.current
        },
        `${r}-title`
      ),
      /* @__PURE__ */ s.jsx(uf, {}, `${r}-divider`)
    );
  }), t.pop(), /* @__PURE__ */ s.jsx("div", { className: Je.wrapper, children: /* @__PURE__ */ s.jsx("div", { className: Je.container, children: t }) });
}, wa = (e) => /* @__PURE__ */ s.jsx(Fn, { fill: e.fill, children: e.options.map((t) => {
  const n = t.value === e.value;
  return /* @__PURE__ */ s.jsx(
    k,
    {
      icon: t.icon,
      iconLabel: t.iconLabel,
      children: t.label,
      onClick: () => {
        n === !1 && e.setValue(t.value);
      },
      disabled: e.disabled || t.disabled,
      fill: e.fill,
      size: e.size,
      style: e.style,
      ...e.highlight ? { highlight: n } : { selected: n }
    },
    t.key || t.label || t.iconLabel
  );
}) });
wa.styles = k.styles;
wa.sizes = k.sizes;
const df = "_full_ivon6_1", ff = "_titles_ivon6_7", mf = "_title_ivon6_7", pf = "_content_ivon6_10", vf = "_outsetContent_ivon6_29", hf = "_outsetTitle_ivon6_38", gf = "_outsetInactive_ivon6_46", yf = "_flatPadding_ivon6_52", bf = "_flatFullHeight_ivon6_56", _f = "_flatContent_ivon6_60", xf = "_flatTitle_ivon6_66", wf = "_flatInactive_ivon6_72", jf = "_flatActive_ivon6_76", fe = {
  full: df,
  titles: ff,
  title: mf,
  content: pf,
  outsetContent: vf,
  outsetTitle: hf,
  outsetInactive: gf,
  flatPadding: yf,
  flatFullHeight: bf,
  flatContent: _f,
  flatTitle: xf,
  flatInactive: wf,
  flatActive: jf
}, tn = {
  UNDEF_SET: '"setActiveTab" must be defined when "active" is defined (Tabs is controlled)',
  DEF_INITIAL: '"initialTab" must be undefined when "active" is defined (Tabs is controlled)',
  LENGTH: "Tabs must have at least one tab",
  DEF_SET: '"setActiveTab" must be undefined when "active" is undefined (Tabs is uncontrolled)',
  UNDEF_TAB: (e) => `Tab "${e}" is not defined`
}, Ef = (e, t) => (n) => {
  const { active: r, setActive: i } = t, a = e.style ?? Hr.styles.outset;
  return /* @__PURE__ */ s.jsx(
    "button",
    {
      className: [
        fe.title,
        Rt.normal,
        a.title,
        r === n.id ? a.active : a.inactive
      ].join(" "),
      onClick: () => i(n.id),
      children: n.title
    },
    n.id
  );
}, Of = (e) => {
  const { tabs: t, activeTab: n, setActiveTab: r, initialTab: i } = e;
  if (t.length < 1) throw Error(tn.LENGTH);
  const a = J.useState(i ?? t[0].id);
  if (n !== void 0) {
    if (r === void 0) throw Error(tn.UNDEF_SET);
    if (i !== void 0) throw Error(tn.DEF_INITIAL);
    return { active: n, setActive: r };
  } else {
    if (r !== void 0) throw Error(tn.DEF_SET);
    return { active: a[0], setActive: a[1] };
  }
}, Hr = (e) => {
  const { tabs: t } = e, n = e.style ?? Hr.styles.outset, r = Of(e), i = t.find((l) => l.id === r.active);
  if (i === void 0) throw Error(tn.UNDEF_TAB(r.active));
  const a = [fe.container, e.fullHeight ? fe.full : ""].join(" ");
  return /* @__PURE__ */ s.jsxs("div", { className: a, children: [
    /* @__PURE__ */ s.jsx("div", { className: fe.titles, children: t.map(Ef(e, r)) }),
    /* @__PURE__ */ s.jsx("div", { className: [fe.content, n.content].join(" "), children: n.renderContent(i.pane(), e) })
  ] });
}, Cf = {
  content: fe.outsetContent,
  title: [fe.outsetTitle, H.radius].join(" "),
  active: [H.strong, _e.strong].join(" "),
  inactive: fe.outsetInactive,
  renderContent: (e, t) => /* @__PURE__ */ s.jsx(
    Vr,
    {
      noPadding: t.noPadding,
      fullHeight: t.fullHeight,
      children: e
    }
  )
}, Tf = {
  content: [fe.flatContent, H.weak].join(" "),
  title: fe.flatTitle,
  active: fe.flatActive,
  inactive: fe.flatInactive,
  renderContent: (e, t) => /* @__PURE__ */ s.jsx(
    "div",
    {
      className: [
        // eslint-disable-next-line react/prop-types
        t.noPadding ? "" : fe.flatPadding,
        // eslint-disable-next-line react/prop-types
        t.fullHeight ? fe.flatFullHeight : ""
      ].join(" "),
      children: e
    }
  )
};
Hr.styles = {
  outset: Cf,
  flat: Tf
};
const Sf = (e) => {
  const { rowKey: t } = e, { expanded: n, setExpanded: r } = e.expandable, i = n.has(t);
  return /* @__PURE__ */ s.jsx(
    k,
    {
      onClick: () => {
        const a = new Set(n);
        i ? a.delete(t) : a.add(t), r(a);
      },
      icon: i ? ae.chevronUp : ae.chevronDown,
      iconLabel: "Expand/collapse row",
      size: k.sizes.smallIcon
    }
  );
}, kf = (e) => {
  const { rowKey: t, selectable: n } = e, { selected: r, setSelected: i } = n, a = (l) => {
    const c = new Set(r);
    l ? c.add(t) : c.delete(t), i(c);
  };
  return /* @__PURE__ */ s.jsx(
    Wn,
    {
      checked: r.has(t),
      setChecked: a,
      children: `Select ${t}`,
      hideLabel: !0
    }
  );
}, Nf = (e) => {
  const { rowKey: t } = e, { selected: n, setSelected: r, radioGroupName: i } = e.selectable;
  if (i === void 0)
    throw Error("radioGroupName must be defined for single-selection");
  return /* @__PURE__ */ s.jsx(
    xa,
    {
      name: i,
      checked: n === t,
      value: t,
      setValue: r,
      children: `Select ${t}`,
      hideLabel: !0
    }
  );
}, $f = (e) => typeof e.selectable.selected == "string", Pf = (e) => typeof e.selectable.selected != "string", Df = (e) => {
  if ($f(e)) return /* @__PURE__ */ s.jsx(Nf, { ...e });
  if (Pf(e)) return /* @__PURE__ */ s.jsx(kf, { ...e });
  throw Error("'props' is either single or multiple");
}, Rf = "_container_1r51r_1", Af = "_column_1r51r_7", ja = {
  container: Rf,
  column: Af
}, If = (e, t) => (n, r, i) => /* @__PURE__ */ s.jsxs("div", { className: ja.container, children: [
  e.selectable !== void 0 && /* @__PURE__ */ s.jsx(Df, { rowKey: i, selectable: e.selectable }),
  e.expandable !== void 0 && /* @__PURE__ */ s.jsx(Sf, { rowKey: i, expandable: t.expandable })
] }), Mf = (e, t) => ({
  className: ja.column,
  title: "",
  render: If(e, t)
}), Ea = (e) => [e.expandable, e.selectable].some((t) => t !== void 0), Lf = ({
  expandable: e,
  selectable: t
}) => {
  if (!e && !t) return 0;
  const n = 24, r = e ? 24 : 0, i = t ? 20 : 0, a = e && t ? 16 : 0;
  return n + r + i + a;
}, zf = (e) => {
  const [t, n] = J.useState(() => (e == null ? void 0 : e.initialExpanded) ?? /* @__PURE__ */ new Set());
  return {
    expanded: (e == null ? void 0 : e.expanded) ?? t,
    setExpanded: (e == null ? void 0 : e.setExpanded) ?? n
  };
}, Ff = (e, t) => e === void 0 ? !1 : typeof e == "string" ? e === t : e.has(t);
[H.weak, _e.strong].join(" ");
const Wf = (e) => {
  const { column: t, row: n } = e, r = typeof t.render == "function" ? t.render(n, e.rowIndex, e.rowKey) : n[t.render], i = e.tableState.columnMetaMap.get(e.columnIndex);
  return /* @__PURE__ */ s.jsx(
    "td",
    {
      className: [
        H.weak,
        e.background,
        (i == null ? void 0 : i.className) ?? "",
        t.className
      ].join(" "),
      style: i == null ? void 0 : i.style,
      children: r
    }
  );
}, Bf = {
  EXPAND_RENDER: "Row is expanded but expandable.render is not defined."
}, Vf = (e) => {
  var i;
  const { tableProps: t, row: n } = e, r = (i = t.expandable) == null ? void 0 : i.render;
  if (r === void 0) throw Error(Bf.EXPAND_RENDER);
  return /* @__PURE__ */ s.jsx(
    "td",
    {
      className: [_e.weak, H.weak].join(" "),
      colSpan: t.columns.length,
      children: r(n)
    }
  );
}, Hf = (e, t) => {
  var a;
  const { tableState: n, tableProps: r } = e;
  return n.expandable.expanded.has(t) || Ff((a = r.selectable) == null ? void 0 : a.selected, t) ? _e.weak : _e.strong;
}, Uf = (e) => {
  var f, h;
  const { tableProps: t, tableState: n, row: r, rowIndex: i } = e, a = t.rowKey(r, i), l = Hf(e, a), c = (h = (f = e.tableProps).rowClassName) == null ? void 0 : h.call(f, r, i), d = /* @__PURE__ */ s.jsx("tr", { className: c, children: t.columns.map((b, g) => /* @__PURE__ */ s.jsx(
    Wf,
    {
      background: l,
      column: b,
      columnIndex: g,
      row: r,
      rowIndex: i,
      rowKey: a,
      tableState: n
    },
    g
  )) }, a);
  if (n.expandable.expanded.has(a) === !1) return [d];
  const p = /* @__PURE__ */ s.jsx("tr", { children: /* @__PURE__ */ s.jsx(Vf, { tableProps: t, row: r }) }, `${a}-expand`);
  return [d, p];
}, qf = (e) => {
  const { tableProps: t, tableState: n } = e, r = [];
  return t.rows.forEach((i, a) => {
    const l = Uf({ row: i, rowIndex: a, tableState: n, tableProps: t });
    r.push(...l);
  }), /* @__PURE__ */ s.jsx("tbody", { children: r });
}, Yf = "_top_uuvsj_1", Gf = "_left_uuvsj_2", Kf = "_right_uuvsj_3", Xf = "_leftLast_uuvsj_28", Zf = "_leftAdjacent_uuvsj_33", Jf = "_rightLast_uuvsj_36", Qf = "_rightAdjacent_uuvsj_41", ut = {
  top: Yf,
  left: Gf,
  right: Kf,
  leftLast: Xf,
  leftAdjacent: Zf,
  rightLast: Jf,
  rightAdjacent: Qf
}, em = (e) => {
  const t = /* @__PURE__ */ new Map();
  if (e.fixed === void 0) return t;
  const n = Ea(e);
  if (e.fixed.firstColumn) {
    const r = n ? 1 : 0;
    r === 1 && t.set(0, { className: ut.left }), t.set(r, {
      className: [ut.left, ut.leftLast].join(" "),
      style: { left: Lf(e) }
    }), t.set(r + 1, { className: ut.leftAdjacent });
  }
  if (e.fixed.lastColumn) {
    const r = e.columns.length + (n ? 1 : 0);
    t.set(r - 1, { className: [ut.right, ut.rightLast].join(" ") }), t.set(r - 2, { className: ut.rightAdjacent });
  }
  return t;
}, tm = (e) => em(e), nm = [H.weak, _e.weak, rt.strong].join(" "), rm = (e) => {
  var n;
  const t = e.tableState.columnMetaMap.get(e.index);
  return /* @__PURE__ */ s.jsx(
    "th",
    {
      className: [
        nm,
        e.column.className,
        (n = e.tableProps.fixed) != null && n.header ? ut.top : "",
        (t == null ? void 0 : t.className) ?? ""
      ].join(" "),
      style: t == null ? void 0 : t.style,
      children: e.column.title
    }
  );
}, im = (e) => /* @__PURE__ */ s.jsx("thead", { children: /* @__PURE__ */ s.jsx("tr", { children: e.tableProps.columns.map((t, n) => /* @__PURE__ */ s.jsx(
  rm,
  {
    column: t,
    index: n,
    tableProps: e.tableProps,
    tableState: e.tableState
  },
  n
)) }) }), am = "_small_adb35_1", om = "_medium_adb35_16", sm = "_large_adb35_31", fr = {
  small: am,
  medium: om,
  large: sm
}, cm = "_container_m73ax_5", lm = "_containerFill_m73ax_11", Oi = {
  container: cm,
  containerFill: lm
}, Oa = (e) => {
  var r;
  const t = { ...e }, n = {
    expandable: zf(t.expandable),
    columnMetaMap: tm(t)
  };
  if (Ea(t)) {
    const i = Mf(t, n);
    t.columns = [i, ...t.columns];
  }
  return /* @__PURE__ */ s.jsxs(
    "table",
    {
      className: [
        Oi.container,
        t.fill ? Oi.containerFill : "",
        _e.strong,
        ((r = t.size) == null ? void 0 : r.cell) ?? Oa.sizes.medium.cell
      ].join(" "),
      children: [
        /* @__PURE__ */ s.jsx(im, { tableProps: t, tableState: n }),
        /* @__PURE__ */ s.jsx(qf, { tableProps: t, tableState: n })
      ]
    }
  );
};
Oa.sizes = {
  large: { cell: fr.large },
  medium: { cell: fr.medium },
  small: { cell: fr.small }
};
const um = "_container_1y0w4_1", dm = "_smallTag_1y0w4_25", fm = "_mediumTag_1y0w4_30", jr = {
  container: um,
  smallTag: dm,
  mediumTag: fm
}, Ca = ({
  children: e,
  color: t,
  size: n,
  forwardedRef: r
}) => /* @__PURE__ */ s.jsx(
  "span",
  {
    className: [H.radius, jr.container, t, n].join(" "),
    children: e,
    ref: r
  }
);
Ca.colors = ba;
Ca.sizes = {
  medium: jr.mediumTag,
  small: jr.smallTag
};
const qn = (e) => ({
  value: e,
  id: e.toString(),
  label: `0${e.toString()}`.slice(-2)
}), mm = [...Array(24).keys()].map(qn), pm = [...Array(60).keys()].map(qn), vm = [0, 15, 30, 45].map(qn), hm = [0, 30].map(qn), gm = {
  1: pm,
  15: vm,
  30: hm,
  60: []
}, ym = (e) => (t) => {
  const n = new Date(e.value.getTime());
  n.setHours(t), n.setSeconds(0), e.setValue(n);
}, bm = (e) => (t) => {
  const n = new Date(e.value.getTime());
  n.setMinutes(t), n.setSeconds(0), e.setValue(n);
}, Ur = (e) => {
  if (e.value.getSeconds() !== 0)
    throw Error("Date value of TimeInput must have 0 second");
  const t = [], n = {
    disabled: e.disabled,
    fill: e.fill,
    style: e.style,
    size: e.size
  }, r = /* @__PURE__ */ s.jsx(
    De,
    {
      ...n,
      value: e.value.getHours(),
      setValue: ym(e),
      options: mm
    }
  );
  if (t.push({ fill: e.fill, element: r }), e.interval !== 60) {
    const i = /* @__PURE__ */ s.jsx(
      De,
      {
        ...n,
        value: e.value.getMinutes(),
        setValue: bm(e),
        options: gm[e.interval]
      }
    );
    t.push({ fill: e.fill, element: i });
  }
  return /* @__PURE__ */ s.jsx(Fn, { fill: e.fill, children: t });
};
Ur.styles = De.styles;
Ur.sizes = De.sizes;
Ur.intervals = {
  minute: 1,
  quarter: 15,
  half: 30,
  hour: 60
};
var Er, Nn = Ua;
if (process.env.NODE_ENV === "production")
  Er = Nn.createRoot, Nn.hydrateRoot;
else {
  var Ci = Nn.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
  Er = function(e, t) {
    Ci.usingClientEntryPoint = !0;
    try {
      return Nn.createRoot(e, t);
    } finally {
      Ci.usingClientEntryPoint = !1;
    }
  };
}
let _m = { data: "" }, xm = (e) => typeof window == "object" ? ((e ? e.querySelector("#_goober") : window._goober) || Object.assign((e || document.head).appendChild(document.createElement("style")), { innerHTML: " ", id: "_goober" })).firstChild : e || _m, wm = /(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g, jm = /\/\*[^]*?\*\/|  +/g, Ti = /\n+/g, dt = (e, t) => {
  let n = "", r = "", i = "";
  for (let a in e) {
    let l = e[a];
    a[0] == "@" ? a[1] == "i" ? n = a + " " + l + ";" : r += a[1] == "f" ? dt(l, a) : a + "{" + dt(l, a[1] == "k" ? "" : t) + "}" : typeof l == "object" ? r += dt(l, t ? t.replace(/([^,])+/g, (c) => a.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g, (d) => /&/.test(d) ? d.replace(/&/g, c) : c ? c + " " + d : d)) : a) : l != null && (a = /^--/.test(a) ? a : a.replace(/[A-Z]/g, "-$&").toLowerCase(), i += dt.p ? dt.p(a, l) : a + ":" + l + ";");
  }
  return n + (t && i ? t + "{" + i + "}" : i) + r;
}, Xe = {}, Ta = (e) => {
  if (typeof e == "object") {
    let t = "";
    for (let n in e) t += n + Ta(e[n]);
    return t;
  }
  return e;
}, Em = (e, t, n, r, i) => {
  let a = Ta(e), l = Xe[a] || (Xe[a] = ((d) => {
    let p = 0, f = 11;
    for (; p < d.length; ) f = 101 * f + d.charCodeAt(p++) >>> 0;
    return "go" + f;
  })(a));
  if (!Xe[l]) {
    let d = a !== e ? e : ((p) => {
      let f, h, b = [{}];
      for (; f = wm.exec(p.replace(jm, "")); ) f[4] ? b.shift() : f[3] ? (h = f[3].replace(Ti, " ").trim(), b.unshift(b[0][h] = b[0][h] || {})) : b[0][f[1]] = f[2].replace(Ti, " ").trim();
      return b[0];
    })(e);
    Xe[l] = dt(i ? { ["@keyframes " + l]: d } : d, n ? "" : "." + l);
  }
  let c = n && Xe.g ? Xe.g : null;
  return n && (Xe.g = Xe[l]), ((d, p, f, h) => {
    h ? p.data = p.data.replace(h, d) : p.data.indexOf(d) === -1 && (p.data = f ? d + p.data : p.data + d);
  })(Xe[l], t, r, c), l;
}, Om = (e, t, n) => e.reduce((r, i, a) => {
  let l = t[a];
  if (l && l.call) {
    let c = l(n), d = c && c.props && c.props.className || /^go/.test(c) && c;
    l = d ? "." + d : c && typeof c == "object" ? c.props ? "" : dt(c, "") : c === !1 ? "" : c;
  }
  return r + i + (l ?? "");
}, "");
function Yn(e) {
  let t = this || {}, n = e.call ? e(t.p) : e;
  return Em(n.unshift ? n.raw ? Om(n, [].slice.call(arguments, 1), t.p) : n.reduce((r, i) => Object.assign(r, i && i.call ? i(t.p) : i), {}) : n, xm(t.target), t.g, t.o, t.k);
}
let Sa, Or, Cr;
Yn.bind({ g: 1 });
let it = Yn.bind({ k: 1 });
function Cm(e, t, n, r) {
  dt.p = t, Sa = e, Or = n, Cr = r;
}
function ht(e, t) {
  let n = this || {};
  return function() {
    let r = arguments;
    function i(a, l) {
      let c = Object.assign({}, a), d = c.className || i.className;
      n.p = Object.assign({ theme: Or && Or() }, c), n.o = / *go\d+/.test(d), c.className = Yn.apply(n, r) + (d ? " " + d : "");
      let p = e;
      return e[0] && (p = c.as || e, delete c.as), Cr && p[0] && Cr(c), Sa(p, c);
    }
    return i;
  };
}
var Tm = (e) => typeof e == "function", Tr = (e, t) => Tm(e) ? e(t) : e, Sm = /* @__PURE__ */ (() => {
  let e = 0;
  return () => (++e).toString();
})(), km = /* @__PURE__ */ (() => {
  let e;
  return () => {
    if (e === void 0 && typeof window < "u") {
      let t = matchMedia("(prefers-reduced-motion: reduce)");
      e = !t || t.matches;
    }
    return e;
  };
})(), Nm = 20, ka = (e, t) => {
  switch (t.type) {
    case 0:
      return { ...e, toasts: [t.toast, ...e.toasts].slice(0, Nm) };
    case 1:
      return { ...e, toasts: e.toasts.map((a) => a.id === t.toast.id ? { ...a, ...t.toast } : a) };
    case 2:
      let { toast: n } = t;
      return ka(e, { type: e.toasts.find((a) => a.id === n.id) ? 1 : 0, toast: n });
    case 3:
      let { toastId: r } = t;
      return { ...e, toasts: e.toasts.map((a) => a.id === r || r === void 0 ? { ...a, dismissed: !0, visible: !1 } : a) };
    case 4:
      return t.toastId === void 0 ? { ...e, toasts: [] } : { ...e, toasts: e.toasts.filter((a) => a.id !== t.toastId) };
    case 5:
      return { ...e, pausedAt: t.time };
    case 6:
      let i = t.time - (e.pausedAt || 0);
      return { ...e, pausedAt: void 0, toasts: e.toasts.map((a) => ({ ...a, pauseDuration: a.pauseDuration + i })) };
  }
}, Dn = [], kt = { toasts: [], pausedAt: void 0 }, At = (e) => {
  kt = ka(kt, e), Dn.forEach((t) => {
    t(kt);
  });
}, $m = { blank: 4e3, error: 4e3, success: 2e3, loading: 1 / 0, custom: 4e3 }, Pm = (e = {}) => {
  let [t, n] = Pe(kt), r = Di(kt);
  et(() => (r.current !== kt && n(kt), Dn.push(n), () => {
    let a = Dn.indexOf(n);
    a > -1 && Dn.splice(a, 1);
  }), []);
  let i = t.toasts.map((a) => {
    var l, c, d;
    return { ...e, ...e[a.type], ...a, removeDelay: a.removeDelay || ((l = e[a.type]) == null ? void 0 : l.removeDelay) || (e == null ? void 0 : e.removeDelay), duration: a.duration || ((c = e[a.type]) == null ? void 0 : c.duration) || (e == null ? void 0 : e.duration) || $m[a.type], style: { ...e.style, ...(d = e[a.type]) == null ? void 0 : d.style, ...a.style } };
  });
  return { ...t, toasts: i };
}, Dm = (e, t = "blank", n) => ({ createdAt: Date.now(), visible: !0, dismissed: !1, type: t, ariaProps: { role: "status", "aria-live": "polite" }, message: e, pauseDuration: 0, ...n, id: (n == null ? void 0 : n.id) || Sm() }), yn = (e) => (t, n) => {
  let r = Dm(t, e, n);
  return At({ type: 2, toast: r }), r.id;
}, ce = (e, t) => yn("blank")(e, t);
ce.error = yn("error");
ce.success = yn("success");
ce.loading = yn("loading");
ce.custom = yn("custom");
ce.dismiss = (e) => {
  At({ type: 3, toastId: e });
};
ce.remove = (e) => At({ type: 4, toastId: e });
ce.promise = (e, t, n) => {
  let r = ce.loading(t.loading, { ...n, ...n == null ? void 0 : n.loading });
  return typeof e == "function" && (e = e()), e.then((i) => {
    let a = t.success ? Tr(t.success, i) : void 0;
    return a ? ce.success(a, { id: r, ...n, ...n == null ? void 0 : n.success }) : ce.dismiss(r), i;
  }).catch((i) => {
    let a = t.error ? Tr(t.error, i) : void 0;
    a ? ce.error(a, { id: r, ...n, ...n == null ? void 0 : n.error }) : ce.dismiss(r);
  }), e;
};
var Rm = (e, t) => {
  At({ type: 1, toast: { id: e, height: t } });
}, Am = () => {
  At({ type: 5, time: Date.now() });
}, sn = /* @__PURE__ */ new Map(), Im = 1e3, Mm = (e, t = Im) => {
  if (sn.has(e)) return;
  let n = setTimeout(() => {
    sn.delete(e), At({ type: 4, toastId: e });
  }, t);
  sn.set(e, n);
}, Lm = (e) => {
  let { toasts: t, pausedAt: n } = Pm(e);
  et(() => {
    if (n) return;
    let a = Date.now(), l = t.map((c) => {
      if (c.duration === 1 / 0) return;
      let d = (c.duration || 0) + c.pauseDuration - (a - c.createdAt);
      if (d < 0) {
        c.visible && ce.dismiss(c.id);
        return;
      }
      return setTimeout(() => ce.dismiss(c.id), d);
    });
    return () => {
      l.forEach((c) => c && clearTimeout(c));
    };
  }, [t, n]);
  let r = cn(() => {
    n && At({ type: 6, time: Date.now() });
  }, [n]), i = cn((a, l) => {
    let { reverseOrder: c = !1, gutter: d = 8, defaultPosition: p } = l || {}, f = t.filter((g) => (g.position || p) === (a.position || p) && g.height), h = f.findIndex((g) => g.id === a.id), b = f.filter((g, j) => j < h && g.visible).length;
    return f.filter((g) => g.visible).slice(...c ? [b + 1] : [0, b]).reduce((g, j) => g + (j.height || 0) + d, 0);
  }, [t]);
  return et(() => {
    t.forEach((a) => {
      if (a.dismissed) Mm(a.id, a.removeDelay);
      else {
        let l = sn.get(a.id);
        l && (clearTimeout(l), sn.delete(a.id));
      }
    });
  }, [t]), { toasts: t, handlers: { updateHeight: Rm, startPause: Am, endPause: r, calculateOffset: i } };
}, zm = it`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`, Fm = it`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`, Wm = it`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`, Bm = ht("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${(e) => e.primary || "#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${zm} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Fm} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${(e) => e.secondary || "#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Wm} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`, Vm = it`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`, Hm = ht("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${(e) => e.secondary || "#e0e0e0"};
  border-right-color: ${(e) => e.primary || "#616161"};
  animation: ${Vm} 1s linear infinite;
`, Um = it`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`, qm = it`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`, Ym = ht("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${(e) => e.primary || "#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Um} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${qm} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${(e) => e.secondary || "#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`, Gm = ht("div")`
  position: absolute;
`, Km = ht("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`, Xm = it`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`, Zm = ht("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Xm} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`, Jm = ({ toast: e }) => {
  let { icon: t, type: n, iconTheme: r } = e;
  return t !== void 0 ? typeof t == "string" ? J.createElement(Zm, null, t) : t : n === "blank" ? null : J.createElement(Km, null, J.createElement(Hm, { ...r }), n !== "loading" && J.createElement(Gm, null, n === "error" ? J.createElement(Bm, { ...r }) : J.createElement(Ym, { ...r })));
}, Qm = (e) => `
0% {transform: translate3d(0,${e * -200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`, ep = (e) => `
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e * -150}%,-1px) scale(.6); opacity:0;}
`, tp = "0%{opacity:0;} 100%{opacity:1;}", np = "0%{opacity:1;} 100%{opacity:0;}", rp = ht("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`, ip = ht("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`, ap = (e, t) => {
  let n = e.includes("top") ? 1 : -1, [r, i] = km() ? [tp, np] : [Qm(n), ep(n)];
  return { animation: t ? `${it(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards` : `${it(i)} 0.4s forwards cubic-bezier(.06,.71,.55,1)` };
};
J.memo(({ toast: e, position: t, style: n, children: r }) => {
  let i = e.height ? ap(e.position || t || "top-center", e.visible) : { opacity: 0 }, a = J.createElement(Jm, { toast: e }), l = J.createElement(ip, { ...e.ariaProps }, Tr(e.message, e));
  return J.createElement(rp, { className: e.className, style: { ...i, ...n, ...e.style } }, typeof r == "function" ? r({ icon: a, message: l }) : J.createElement(J.Fragment, null, a, l));
});
Cm(J.createElement);
Yn`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`;
var Sr = ce;
const op = "_container_awm9c_1", sp = "_size_awm9c_10", cp = "_dark_awm9c_15", lp = "_icon_awm9c_21", up = "_children_awm9c_25", dp = "_button_awm9c_29", $t = {
  container: op,
  size: sp,
  dark: cp,
  icon: lp,
  children: up,
  button: dp
}, fp = (e) => {
  const t = e.close;
  return t === void 0 ? null : /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
    /* @__PURE__ */ s.jsx(Ii, { color: "strong" }),
    /* @__PURE__ */ s.jsx(xe, { size: 12 }),
    /* @__PURE__ */ s.jsx("div", { className: $t.button, children: /* @__PURE__ */ s.jsx(
      k,
      {
        size: k.sizes.smallIcon,
        style: k.styles.flat,
        icon: ae.cross,
        iconLabel: "Close",
        onClick: () => t()
      }
    ) })
  ] });
}, pt = (e) => /* @__PURE__ */ s.jsx("div", { children: /* @__PURE__ */ s.jsxs(
  "div",
  {
    className: [
      $t.container,
      pt.styles.main,
      e.type.className
    ].join(" "),
    children: [
      /* @__PURE__ */ s.jsx("div", { className: [$t.icon, e.type.iconCls].join(" "), children: /* @__PURE__ */ s.jsx(mt, { display: "block", component: e.type.iconComponent }) }),
      /* @__PURE__ */ s.jsx(xe, { size: 12 }),
      /* @__PURE__ */ s.jsx("div", { className: $t.children, children: /* @__PURE__ */ s.jsx(aa, { children: e.children }) }),
      /* @__PURE__ */ s.jsx(xe, { size: 12 }),
      /* @__PURE__ */ s.jsx(fp, { ...e })
    ]
  }
) });
pt.types = {
  success: {
    iconComponent: ae.success,
    iconCls: rt.successWeak,
    className: $t.dark
  },
  failure: {
    iconComponent: ae.error,
    iconCls: rt.failureWeak,
    className: $t.dark
  }
};
pt.styles = {
  main: [$t.size, Gt.boxStrong, H.radius].join(" ")
};
const mp = "_container_gupuq_1", pp = "_item_gupuq_9", Si = {
  container: mp,
  item: pp
}, vp = (e) => {
  switch (e) {
    case "success":
      return pt.types.success;
    case "error":
      return pt.types.failure;
    case "blank":
      throw Error(`Unknown type: "${e}"`);
    case "loading":
      throw Error(`Unknown type: "${e}"`);
    default:
      throw Error(`Unknown type: "${e}"`);
  }
}, hp = () => {
  const { toasts: e, handlers: t } = Lm(), { startPause: n, endPause: r, calculateOffset: i, updateHeight: a } = t, l = (c) => {
    if (typeof c.message != "string")
      throw Error("Toast message must be a string");
    const p = i(c, { reverseOrder: !1, margin: 8 }), f = (h) => {
      if (!h || c.height) return;
      const b = h.getBoundingClientRect().height;
      a(c.id, b);
    };
    return /* @__PURE__ */ s.jsx(
      "div",
      {
        ref: f,
        className: Si.item,
        style: {
          opacity: c.visible ? 1 : 0,
          pointerEvents: c.visible ? "auto" : "none",
          transform: `translate(-50%, ${p}px)`
        },
        children: /* @__PURE__ */ s.jsx(
          pt,
          {
            type: vp(c.type),
            children: c.message,
            close: () => Sr.dismiss(c.id)
          }
        )
      },
      c.id
    );
  };
  return /* @__PURE__ */ s.jsx(
    "div",
    {
      className: Si.container,
      onMouseEnter: n,
      onMouseLeave: r,
      children: e.map(l)
    }
  );
}, gp = {
  success: {
    handler: Sr.success,
    paneType: pt.types.success
  },
  failure: {
    handler: Sr.error,
    paneType: pt.types.failure
  }
}, Na = { current: !1 }, yp = async (e) => {
  const t = document.createElement("div"), n = Er(t);
  document.body.append(t), n.render(/* @__PURE__ */ s.jsx(hp, {})), await new Promise((r) => setTimeout(r, 100)), Na.current = !0, e(t);
}, bp = async (e, t) => {
  Na.current === !1 && await new Promise(yp), e.handler(t);
};
bp.types = gp;
const Dt = (e) => e.isLeaf === void 0 ? e.children === void 0 : e.isLeaf, _p = (e) => {
  var n;
  const t = ((n = e.getRowActions) == null ? void 0 : n.call(e, e.node)) ?? [];
  return t.length === 0 ? null : /* @__PURE__ */ s.jsx(
    "div",
    {
      onClick: (r) => {
        r.stopPropagation();
      },
      children: /* @__PURE__ */ s.jsx(
        ia,
        {
          button: {
            icon: ae.kebab,
            iconLabel: "More actions",
            style: k.styles.flat,
            size: k.sizes.small
          },
          items: t
        }
      )
    }
  );
}, xp = "_container_fwvth_1", wp = "_toggle_fwvth_13", jp = "_tab_fwvth_17", Ep = "_actions_fwvth_19", Op = "_label_fwvth_23", nn = {
  container: xp,
  toggle: wp,
  tab: jp,
  actions: Ep,
  label: Op
}, ki = () => /* @__PURE__ */ s.jsx("div", { className: [k.sizes.smallIcon.mainClassName, nn.tab].join(" ") }), Ni = async (e) => {
  const t = new Set(e.expanded);
  t.has(e.node.id) ? t.delete(e.node.id) : t.add(e.node.id), e.setExpanded(t);
}, Cp = (e) => {
  const t = e.expanded.has(e.node.id), n = e.selected.has(e.node.id), r = Dt(e.node);
  return /* @__PURE__ */ s.jsxs(
    "div",
    {
      className: [
        nn.container,
        k.styles.flat.mainClassName,
        k.colors.none.flat.mainClassName,
        n ? k.colors.none.flat.selectedClassName : ""
      ].join(" "),
      onClick: () => {
        r || e.parentMode === "select" ? e.setSelected(/* @__PURE__ */ new Set([e.node.id])) : Ni(e);
      },
      children: [
        [...Array(e._level ?? 0)].map((i, a) => /* @__PURE__ */ s.jsx(ki, {}, a)),
        /* @__PURE__ */ s.jsx("div", { className: nn.toggle, children: r === !1 ? /* @__PURE__ */ s.jsx(
          k,
          {
            icon: t ? ae.chevronDown : ae.chevronRight,
            iconLabel: t ? "Collapse group" : "Expand group",
            onClick: () => Ni(e),
            style: k.styles.flat,
            size: k.sizes.smallIcon
          }
        ) : /* @__PURE__ */ s.jsx(ki, {}) }),
        /* @__PURE__ */ s.jsx("div", { className: nn.label, children: e.node.label }),
        /* @__PURE__ */ s.jsx("div", { className: nn.actions, children: /* @__PURE__ */ s.jsx(_p, { ...e }) })
      ]
    }
  );
}, Tp = (e, t) => e.id.localeCompare(t.id), $a = (e) => {
  const { node: t, id: n, addNode: r, sort: i } = e;
  if (t.id === n) {
    if (Dt(t)) throw Error("Cannot add node to a leaf");
    if (t.children === void 0) return t;
    const a = [...t.children, r];
    return i && a.sort(Tp), { ...t, children: a };
  } else {
    if (Dt(t) || t.children === void 0) return t;
    const a = t.children.map((l) => $a({ node: l, id: n, addNode: r, sort: i }));
    return { ...t, children: a };
  }
}, Pa = (e) => {
  const { node: t, id: n } = e;
  return t.id === n ? !0 : Dt(t) || t.children === void 0 ? !1 : t.children.some((r) => Pa({ node: r, id: n }));
}, Da = async (e) => {
  const { loadChildren: t, node: n } = e;
  if (Dt(n) || n.children === void 0) return n;
  const r = new Map(n.children.map((c) => [c.id, c])), a = (await t(n)).map(async (c) => {
    const d = r.get(c.id);
    return await Da({ loadChildren: t, node: d === void 0 ? c : d });
  }), l = await Promise.all(a);
  return { ...n, children: l };
}, Ra = (e) => {
  const { tree: t, deleteId: n } = e;
  if (Dt(t) || t.children === void 0) return t;
  const r = t.children.filter((i) => i.id !== n).map((i) => Ra({ tree: i, deleteId: n }));
  return { ...t, children: r };
}, Aa = (e) => {
  const { current: t, id: n, key: r, value: i } = e;
  if (t.id === n)
    return { ...t, [r]: i };
  if (t.children !== void 0) {
    const a = t.children.map((l) => Aa({ current: l, id: n, key: r, value: i }));
    return { ...t, children: a };
  } else
    return t;
}, Hp = {
  addTreeNode: $a,
  isTreeNodeExist: Pa,
  isTreeLeaf: Dt,
  refreshTree: Da,
  removeTreeNode: Ra,
  updateTreeNode: Aa
}, Sp = (e) => (t) => /* @__PURE__ */ Ri(
  kp,
  {
    ...e,
    key: t.id,
    _level: (e._level ?? 0) + 1,
    node: t
  }
), kp = (e) => {
  const t = e.expanded.has(e.node.id), { loadChildren: n, node: r } = e;
  et(() => {
    t && (n == null || n(r));
  }, [n, t, r]);
  const i = /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
    /* @__PURE__ */ s.jsx(Cp, { ...e }),
    e.node.children && t && /* @__PURE__ */ s.jsx(s.Fragment, { children: e.node.children.map(Sp(e)) })
  ] });
  return e._level === 0 ? /* @__PURE__ */ s.jsx("div", { children: i }) : i;
};
export {
  Pp as Background,
  Ii as Border,
  k as Button,
  us as ButtonChildren,
  Fn as ButtonGroup,
  ia as ButtonMenu,
  Wn as Checkbox,
  Fr as DateInput,
  Ue as Dialog,
  Dp as DivEm,
  Rp as DivGrow,
  xe as DivPx,
  zp as FormError,
  Fp as FormField,
  mt as Icon,
  Se as Input,
  _a as Menu,
  Ip as MutedDiv,
  Mp as MutedSpan,
  Wp as Pagination,
  Vr as Pane,
  aa as Paragraph,
  hn as Popover,
  tt as ProgressCircle,
  xa as Radio,
  Kd as RadioGroup,
  De as Select,
  Vp as Steps,
  Ap as Strong,
  wa as Switcher,
  Oa as Table,
  Hr as Tabs,
  Ca as Tag,
  mn as TextArea,
  Ur as TimeInput,
  pt as ToastPane,
  Du as Tooltip,
  Pu as TooltipPane,
  kp as Tree,
  Hp as TreeUtils,
  _e as background,
  H as border,
  Lp as categoryColorNames,
  ba as categoryColors,
  ae as coreIcons,
  Rt as outline,
  Bp as scrollbar,
  Gt as shadow,
  rt as text,
  bp as toast
};
