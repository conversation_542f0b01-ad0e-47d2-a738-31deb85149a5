{"extends": "@tsconfig/vite-react/tsconfig.json", "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.lib.tsbuildinfo", "target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": false, "jsx": "react-jsx", "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": false, "declaration": true, "declarationMap": true, "emitDeclarationOnly": true, "outDir": "./dist"}, "include": ["src/core/**/*"], "exclude": ["src/docs/**/*", "src/gallery/**/*", "src/old-docs/**/*", ".storybook/**/*"]}