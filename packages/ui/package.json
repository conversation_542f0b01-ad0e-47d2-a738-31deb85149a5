{"name": "@moai/ui", "private": true, "version": "0.0.0", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"dev": "vite build --watch", "build": "vite build", "type-check": "tsc --noEmit", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@tippyjs/react": "^4.2.6", "color": "^4.2.3", "formik": "^2.4.6", "headless": "link:@tippyjs/react/headless", "modern-normalize": "^2.0.0", "react": "^18.3.1", "react-day-picker": "^9.0.4", "react-dom": "^18.3.1", "react-hook-form": "^7.56.2", "react-hot-toast": "^2.4.1", "react-icons": "^5.2.1", "react-popper": "^2.3.0"}, "devDependencies": {"@babel/parser": "^7.25.0", "@prettier/sync": "^0.5.2", "@tsconfig/vite-react": "^3.0.2", "@types/color": "^3.0.6", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.9", "typescript": "^5.5.4", "vite": "^5.3.5"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}}