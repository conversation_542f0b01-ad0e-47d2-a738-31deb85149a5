**Heads up!** This project is no longer in active development. It will be [archived](https://docs.github.com/en/repositories/archiving-a-github-repository/archiving-repositories) soon. Some suggestions:

- Build your own UI kit with a good foundation like [<PERSON>dix](https://www.radix-ui.com/) or [Headless UI](https://headlessui.com/). This is what I'm actually doing these days myself.
- Fork this project. You have my sword, and bow, and axe, and only 22 unresolved bugs.
- Refactor <PERSON><PERSON> to utilise <PERSON>dix (which is what I wanted to do, if I don't need to pay the bills)

<hr />

# Moai UI Kit 🗿

A React Component Library, Where Buttons Look Like Buttons.

![Moai preview](https://user-images.githubusercontent.com/5953369/123528873-5b3de480-d715-11eb-8c02-50a04f2d83f9.png)

- Docs: [moai.thien.do](https://moai.thien.do)
- NPM: [@moai/core](http://npmjs.com/package/@moai/core)
- License: [MIT](./LICENSE)

### Contributing

- [Contributing Overview](./.github/CONTRIBUTING.md): Report bugs, request features, write code and improve documentation.
- [Design Principles](https://moai.thien.do/?path=/docs/intro-principles--page): The unique traits that lead Moai's design and development.
- [Development](./.github/DEVELOP.md): Codebase overview and local development setup.
- [Code of Conduct](./.github/CODE_OF_CONDUCT.md): How we treat each other.
