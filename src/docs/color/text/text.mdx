import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@storybook/addon-docs";
import { ColorText } from "./text";

<Meta title="Patterns/Color/Text" />

# Text Color

The `text` utility contains classes that set the [CSS `color`][1] property.
This is the recommended way to set color for texts and icons since they
automatically change based on the current theme and always conform to the
[WCAG of contrast ratios][3] at AA level.

```tsx
import { text } from "@moai/core";

<span className={text.muted}>Text</span>;
```

## Gray colors

There are 2 gray colors in the `text` utility. They can be used for both texts
and icons due to their high contrast ratios:

<ColorText
  rows={[
    { key: "normal", usage: "both" },
    { key: "muted", usage: "both" },
  ]}
/>

The `normal` value sets the color back to the default text color, e.g. a
near-black in light theme. Since it's the app's default, you usually don't need
to use this class, except to override an inherited color from a parent.

The `muted` value dims the color. It is usually used for secondary texts, like
labels and descriptive texts. Their contrast ratios are always above 4.5 to
[ensure legibility][3] in both light and dark themes.

## Semantic colors

There are 3 semantic colors in the `text` utility. Each has a `Strong` version
for texts (contrast 4.5+) and a `Weak` version for icons (contrast 3+):

<ColorText
  rows={[
    { key: "highlightStrong", usage: "text" },
    { key: "highlightWeak", usage: "icon" },
    { key: "successStrong", usage: "text" },
    { key: "successWeak", usage: "icon" },
    { key: "failureStrong", usage: "text" },
    { key: "failureWeak", usage: "icon" },
  ]}
/>

Semantic colors are used to communicate meaning and intention to users.
However, they should not be the only method as some users [may not recognize][4]
them. Other methods, such as helper texts or icons, should be used together to
ensure good accessibility.

Moai uses green for positive intentions and red for negative ones. This may
not work for some users whose culture perceives red as a positive color. In
these cases, you may want to [customize][6] the `text` utility to follow your
users.

## See also

- For static colors that don't change based on the current theme, see
  [Static Colors][7].
- For a wide range of colors that don't have semantic meaning, see
  [Category Colors][8].

[1]: https://developer.mozilla.org/en-US/docs/Web/CSS/color
[3]: https://developer.mozilla.org/en-US/docs/Web/Accessibility/Understanding_WCAG/Perceivable/Color_contrast
[4]: https://en.wikipedia.org/wiki/Color_blindness
[6]: /docs/draft-extension--docs
[7]: /docs/patterns-color-static--docs
[8]: /docs/patterns-color-category--docs
