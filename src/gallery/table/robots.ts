export interface Robot {
  id: string;
  name: {
    first: string;
    last: string;
  };
  avatar: string;
  MAC: string;
  email: string;
  lastSeen: string;
  materials: string[];
  deployed: boolean;
  color: string;
  note: string;
}

// Generated using "./robo-schema.json" via https://json-schema-faker.js.org/
export const ROBOTS: Robot[] = [
  {
    id: "5e330087-2225-4ae8-8d84-43e93b0ff4ae",
    name: {
      first: "<PERSON>",
      last: "Romaguera",
    },
    avatar: "https://robohash.org/Jaquan38.png?size=100x100&set=set1",
    MAC: "de:8d:69:97:9c:52",
    email: "<PERSON>.<PERSON><PERSON><PERSON>@yahoo.com",
    lastSeen: "Sun Feb 14 2021 21:02:38 GMT+0700 (Indochina Time)",
    materials: ["Plastic", "Frozen", "Soft", "Granite"],
    deployed: true,
    color: "#265f27",
    note: "Aspernatur distinctio placeat natus velit voluptas doloremque dolore similique. Facere quos quas. Eum ut alias dignissimos. Eligendi quam rerum. Fugit est cumque iusto.",
  },
  {
    id: "45cbfffe-501c-4c3f-ae1b-64ec3bcb7548",
    name: {
      first: "<PERSON>sey",
      last: "Koelpin",
    },
    avatar: "https://robohash.org/Anahi_Jacobi.png?size=100x100&set=set1",
    MAC: "4b:6f:0d:02:a6:81",
    email: "<EMAIL>",
    lastSeen: "Sat Aug 15 2020 05:48:41 GMT+0700 (Indochina Time)",
    materials: ["Wooden", "Granite", "Fresh", "Frozen", "Rubber"],
    deployed: false,
    color: "#772572",
    note: "Rerum laudantium quis harum. Sit delectus vel tempora aliquid debitis. Hic officiis voluptatum labore voluptatibus et sit. Molestiae aut incidunt quod. Nisi iusto suscipit deleniti in. Aut ea nihil illo.",
  },
  {
    id: "4ac7c89b-fff1-455e-a32d-25900a8f0c57",
    name: {
      first: "Enos",
      last: "Breitenberg",
    },
    avatar: "https://robohash.org/Amelia.Flatley.png?size=100x100&set=set1",
    MAC: "04:b0:5f:66:7d:15",
    email: "<EMAIL>",
    lastSeen: "Mon Jun 29 2020 15:00:32 GMT+0700 (Indochina Time)",
    materials: ["Concrete"],
    deployed: true,
    color: "#6c724b",
    note: "Minima animi explicabo repudiandae quo aliquam quis occaecati exercitationem. Totam laboriosam nihil omnis optio corrupti. Laboriosam ut sed voluptas vero optio quos sed. Praesentium quia quia eligendi nostrum soluta voluptates aliquid perferendis. Velit occaecati architecto vel repellendus. Perspiciatis qui ea tempore.",
  },
  {
    id: "8be9ff9e-5dfb-43c0-9ec8-d84e0d5630cb",
    name: {
      first: "Kasey",
      last: "Hintz",
    },
    avatar: "https://robohash.org/Jermain_Schuster.png?size=100x100&set=set1",
    MAC: "0d:d4:73:e3:ea:03",
    email: "<EMAIL>",
    lastSeen: "Sun Jun 14 2020 18:59:40 GMT+0700 (Indochina Time)",
    materials: ["Metal", "Rubber", "Frozen"],
    deployed: false,
    color: "#7b7520",
    note: "Consequatur ex praesentium odit fugiat sequi reiciendis magnam beatae. Ut sit et aut ullam quas quos. Eos voluptatum quis esse inventore et.",
  },
  {
    id: "d8e77b79-2c9b-4a5b-8095-d91434407a07",
    name: {
      first: "Benton",
      last: "Murphy",
    },
    avatar: "https://robohash.org/Rocky.Keebler.png?size=100x100&set=set1",
    MAC: "d7:06:5a:3e:a7:95",
    email: "<EMAIL>",
    lastSeen: "Thu Feb 11 2021 05:21:18 GMT+0700 (Indochina Time)",
    materials: ["Frozen", "Concrete", "Rubber"],
    deployed: false,
    color: "#755c38",
    note: "Sint provident non officia est ut quaerat aut excepturi omnis. Est excepturi porro aspernatur distinctio et. Nihil aut quod dolorem provident. Quia deserunt totam vitae ipsum. Soluta ipsum est debitis.",
  },
  {
    id: "2aeb431e-a502-4657-bd6f-5c781bb95276",
    name: {
      first: "Aaliyah",
      last: "Beatty",
    },
    avatar: "https://robohash.org/Kathleen43.png?size=100x100&set=set1",
    MAC: "8b:b2:a8:9f:28:34",
    email: "<EMAIL>",
    lastSeen: "Wed Apr 08 2020 15:26:29 GMT+0700 (Indochina Time)",
    materials: ["Concrete", "Steel", "Wooden"],
    deployed: true,
    color: "#180232",
    note: "Aliquid odit odit sed. Delectus rem repellat sint necessitatibus consequatur. Earum quia maiores odio vel.",
  },
  {
    id: "699561fa-d37a-4eb7-be61-847651e79ad8",
    name: {
      first: "Asha",
      last: "Tromp",
    },
    avatar: "https://robohash.org/Oran_Nitzsche91.png?size=100x100&set=set1",
    MAC: "5e:2a:ab:b8:ec:40",
    email: "<EMAIL>",
    lastSeen: "Fri Oct 09 2020 11:27:07 GMT+0700 (Indochina Time)",
    materials: ["Wooden", "Rubber", "Granite", "Concrete", "Fresh"],
    deployed: false,
    color: "#337127",
    note: "Qui qui quia. Consequuntur qui deserunt et laboriosam placeat alias aut quia quis. Omnis non sequi.",
  },
  {
    id: "4c833f0e-fa28-4cff-89aa-d983026644ce",
    name: {
      first: "Mekhi",
      last: "Feeney",
    },
    avatar: "https://robohash.org/Nichole59.png?size=100x100&set=set1",
    MAC: "d7:94:b2:6b:da:bd",
    email: "<EMAIL>",
    lastSeen: "Thu Jul 16 2020 09:53:53 GMT+0700 (Indochina Time)",
    materials: ["Metal", "Steel", "Plastic"],
    deployed: true,
    color: "#416240",
    note: "Ipsam eos nihil incidunt et aut quae quia. Temporibus molestias pariatur consequuntur commodi culpa error ea cumque. Ipsum sint rem odio et et est est quis.",
  },
  {
    id: "3cd03850-0a30-40de-8905-26f3810252c0",
    name: {
      first: "Quinten",
      last: "Dickinson",
    },
    avatar: "https://robohash.org/Rubie94.png?size=100x100&set=set1",
    MAC: "4d:0f:89:6f:89:d9",
    email: "<EMAIL>",
    lastSeen: "Thu Feb 25 2021 08:42:28 GMT+0700 (Indochina Time)",
    materials: ["Steel", "Wooden", "Concrete"],
    deployed: false,
    color: "#7f1a25",
    note: "Voluptas quia est et. Recusandae voluptatem qui ducimus enim. Sapiente maiores ut eos ut ad est ab.",
  },
  {
    id: "a95d9e99-c253-4e55-a49e-9d8da9549a21",
    name: {
      first: "Mitchell",
      last: "Boehm",
    },
    avatar: "https://robohash.org/Matteo53.png?size=100x100&set=set1",
    MAC: "dd:41:3b:95:f9:2e",
    email: "<EMAIL>",
    lastSeen: "Sat Jul 25 2020 01:17:37 GMT+0700 (Indochina Time)",
    materials: ["Frozen", "Steel", "Plastic", "Wooden", "Concrete"],
    deployed: true,
    color: "#520438",
    note: "Tempora veniam iure. Qui necessitatibus aut. Autem natus voluptas rerum. Officiis libero animi fugit est praesentium impedit odio.",
  },
  {
    id: "3b024d91-402f-4490-8537-37ac09313c60",
    name: {
      first: "Dulce",
      last: "Cremin",
    },
    avatar: "https://robohash.org/Jaydon51.png?size=100x100&set=set1",
    MAC: "95:77:52:71:57:92",
    email: "<EMAIL>",
    lastSeen: "Thu Jul 30 2020 21:15:09 GMT+0700 (Indochina Time)",
    materials: ["Fresh", "Steel", "Concrete"],
    deployed: false,
    color: "#7e7b47",
    note: "Aut in quia quos nihil sit explicabo ut. Quo et facilis at voluptates alias omnis ullam molestiae ea. Nesciunt nulla tenetur et nihil et esse ex omnis eius. Consequatur numquam provident omnis deleniti dicta voluptatum voluptatum at. Sapiente nemo laborum nulla aut quo quaerat unde ea. Earum repellat perspiciatis excepturi incidunt eligendi.",
  },
  {
    id: "e762ac31-7678-4fe5-9253-278beef8bf3a",
    name: {
      first: "Charley",
      last: "Douglas",
    },
    avatar: "https://robohash.org/Daron.Pagac.png?size=100x100&set=set1",
    MAC: "fb:b4:3f:00:4d:de",
    email: "<EMAIL>",
    lastSeen: "Thu Mar 05 2020 10:39:32 GMT+0700 (Indochina Time)",
    materials: ["Soft", "Cotton"],
    deployed: true,
    color: "#6c3f10",
    note: "Est culpa aut magnam. Enim voluptates voluptates modi eligendi. Et saepe ut quisquam mollitia et sint.",
  },
  {
    id: "23cc7dd8-1f73-4c79-8aae-069aa7436c81",
    name: {
      first: "Carter",
      last: "Hauck",
    },
    avatar: "https://robohash.org/Estefania.Gutmann.png?size=100x100&set=set1",
    MAC: "54:56:ba:d3:51:4e",
    email: "<EMAIL>",
    lastSeen: "Thu Oct 15 2020 19:46:30 GMT+0700 (Indochina Time)",
    materials: ["Cotton", "Soft"],
    deployed: false,
    color: "#664439",
    note: "Ut nam velit at ea sunt rem cumque illum. Sed vero animi quod molestiae expedita architecto nostrum inventore. Saepe fugiat sint molestiae aut consequatur. Voluptatum ea delectus eveniet ipsam quasi pariatur.",
  },
  {
    id: "d262878b-fa54-4b5f-b8db-6be02b8020ba",
    name: {
      first: "Marion",
      last: "Hansen",
    },
    avatar: "https://robohash.org/Richard9.png?size=100x100&set=set1",
    MAC: "24:d7:20:ec:1c:6c",
    email: "<EMAIL>",
    lastSeen: "Fri Mar 06 2020 15:26:05 GMT+0700 (Indochina Time)",
    materials: ["Concrete", "Soft", "Fresh", "Steel"],
    deployed: false,
    color: "#327702",
    note: "Eos quia id veritatis accusamus quod. Minus nihil nulla et tempore enim facilis at sit ea. Dolor aspernatur ducimus ea et. Officia aut voluptatibus eum dolorum dolor rerum. Aut amet quae at veritatis ab. Animi at non tempore.",
  },
  {
    id: "cbc46f06-d9c0-4661-aeaf-a6df09be6326",
    name: {
      first: "Glennie",
      last: "Ullrich",
    },
    avatar: "https://robohash.org/Lorenzo24.png?size=100x100&set=set1",
    MAC: "62:f6:90:fd:13:15",
    email: "<EMAIL>",
    lastSeen: "Thu Apr 02 2020 02:12:54 GMT+0700 (Indochina Time)",
    materials: ["Fresh", "Rubber", "Soft", "Frozen"],
    deployed: false,
    color: "#302332",
    note: "Aspernatur quas unde voluptatem perspiciatis. Veritatis omnis officiis. Error et laboriosam modi numquam. Minima et similique aspernatur vitae quis dolorem. Voluptatum dolore voluptatem et et minima.",
  },
  {
    id: "3bd3c85c-2ecc-4ed4-b9f0-ef19fed7cb45",
    name: {
      first: "Cristopher",
      last: "Gulgowski",
    },
    avatar: "https://robohash.org/Hilda_Bode.png?size=100x100&set=set1",
    MAC: "5e:73:e1:cf:ba:1a",
    email: "<EMAIL>",
    lastSeen: "Thu Jul 09 2020 01:28:37 GMT+0700 (Indochina Time)",
    materials: ["Metal", "Wooden", "Granite", "Rubber"],
    deployed: true,
    color: "#377b05",
    note: "Repudiandae quasi sunt iure eveniet hic vel. Dolor veniam quae et libero rerum eaque ut libero vel. Omnis qui fugit. Excepturi provident ut et.",
  },
  {
    id: "d0401130-93e0-43f3-994b-266cfa399595",
    name: {
      first: "Myrl",
      last: "Considine",
    },
    avatar: "https://robohash.org/Damon7.png?size=100x100&set=set1",
    MAC: "29:18:60:68:58:4a",
    email: "<EMAIL>",
    lastSeen: "Fri Apr 03 2020 05:32:55 GMT+0700 (Indochina Time)",
    materials: ["Steel"],
    deployed: true,
    color: "#097761",
    note: "Ullam beatae error dicta necessitatibus. Nostrum mollitia dolore odio iusto quasi. Reprehenderit incidunt consequatur hic. Vel laborum quidem iste quam quisquam officia.",
  },
  {
    id: "2265356f-f594-49c3-a372-fdc9544952c1",
    name: {
      first: "Rod",
      last: "Considine",
    },
    avatar: "https://robohash.org/Cristina_Smitham72.png?size=100x100&set=set1",
    MAC: "c8:ae:f8:4c:34:a1",
    email: "<EMAIL>",
    lastSeen: "Thu Nov 26 2020 17:51:44 GMT+0700 (Indochina Time)",
    materials: ["Cotton", "Soft"],
    deployed: false,
    color: "#480c54",
    note: "Itaque laboriosam laudantium voluptatibus. Aut aut esse et corrupti alias sit. Animi ipsam voluptatem nihil doloremque earum labore soluta.",
  },
  {
    id: "62c5e7ec-d279-4550-9840-5ea78cd13dd2",
    name: {
      first: "Cathy",
      last: "Auer",
    },
    avatar: "https://robohash.org/Olen34.png?size=100x100&set=set1",
    MAC: "ce:1d:dd:91:ea:15",
    email: "<EMAIL>",
    lastSeen: "Mon Aug 03 2020 19:10:23 GMT+0700 (Indochina Time)",
    materials: ["Frozen", "Metal", "Rubber"],
    deployed: false,
    color: "#312676",
    note: "Enim hic a reiciendis. Praesentium illum rerum velit et sed eaque. Asperiores rerum temporibus nihil molestiae aperiam non nostrum.",
  },
  {
    id: "954d818a-0062-4f52-9853-aca2bb0bc90d",
    name: {
      first: "Nathanial",
      last: "Feeney",
    },
    avatar: "https://robohash.org/Velda66.png?size=100x100&set=set1",
    MAC: "71:8d:39:fe:9b:68",
    email: "<EMAIL>",
    lastSeen: "Fri Aug 21 2020 11:51:18 GMT+0700 (Indochina Time)",
    materials: ["Metal", "Cotton"],
    deployed: false,
    color: "#491174",
    note: "Magnam tenetur tempora. Eveniet expedita veniam perspiciatis quia odit ab ullam. Sunt cum est enim unde. Quo atque quo debitis. Tempora et excepturi. Earum et eos maxime sapiente velit aperiam.",
  },
];

export const MATERIALS: string[] = (() => {
  const set = new Set<string>();
  ROBOTS.forEach((robot) => {
    robot.materials.forEach((material) => {
      if (set.has(material) === false) set.add(material);
    });
  });
  return [...set];
})();
