.wrapper {
  overflow: auto;
}

.container {
  display: flex;
  align-items: center;
  min-width: max-content;
  padding: 16px;
}

.step {
  display: flex;
  align-items: center;
}

.icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  border-radius: 32px;
  font-weight: 600;

  display: flex;
  align-items: center;
  justify-content: center;
}

.icon.done {
  background-color: var(--highlight-5);
  color: var(--gray-0);
}

:global(.light) .icon.notDone {
  background-color: var(--gray-2);
}

:global(.dark) .icon.notDone {
  background-color: var(--gray-6);
}

.divider {
  flex: 1 0 auto;
  width: 16px;
  border-top-style: solid;
  border-top-width: 1px;
  margin: 0 16px;
}

.title.current {
  font-weight: 600;
}
