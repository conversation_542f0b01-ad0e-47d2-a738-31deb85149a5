.fill {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.container {
  position: fixed;
  z-index: var(--z-overlay-base);

  display: flex;
  align-items: center;
  justify-content: center;
}

.backdrop {
  position: absolute;
  opacity: 0.8;
}

.dialog {
  position: relative;
}

.header {
  padding: 24px;
}

.body {
  padding: 24px;
}

.footer {
  padding: 0 24px;
}

.footerBody {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.widthFixed {
  width: 320px;
}

.widthContent {
  width: auto;
}
