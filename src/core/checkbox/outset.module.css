.input {
  transition:
    background-color 0.1s,
    box-shadow 0.1s,
    outline 0.2s ease-out;
  border: solid 1px transparent;
  --shadow-size: 0px 0.5px 2px;
  --inset-shadow: inset 0px 1px 0px;
}

/* BASE COLOR */

:global(.light) .input {
  --shadow: var(--shadow-size) rgba(0, 0, 0, 0.1);
  background-color: var(--gray-0);
  box-shadow:
    var(--shadow),
    var(--inset-shadow) var(--white);
  border-color: var(--gray-2);
}
:global(.light) .input:hover {
  background-color: var(--white);
}
:global(.light) .input:active {
  background-color: var(--gray-1);
  box-shadow: none;
}

:global(.dark) .input {
  --shadow: var(--shadow-size) rgba(0, 0, 0, 0.3);
  background-color: var(--gray-6);
  box-shadow:
    var(--shadow),
    var(--inset-shadow) var(--gray-5);
  border-color: var(--black);
}
:global(.dark) .input:hover {
  background-color: var(--gray-5);
}
:global(.dark) .input:active {
  background-color: var(--gray-7);
  box-shadow: none;
}

/* CHECKED */

:global(.light) .input:checked,
:global(.light) .input[type="checkbox"]:indeterminate,
:global(.dark) .input:checked,
:global(.dark) .input[type="checkbox"]:indeterminate {
  background-color: var(--highlight-5);
  box-shadow:
    var(--shadow),
    var(--inset-shadow) var(--highlight-4);
}

:global(.light) .input:checked,
:global(.light) .input[type="checkbox"]:indeterminate {
  --shadow: var(--shadow-size) rgba(0, 0, 0, 0.2);
  border-color: var(--highlight-7);
}

:global(.dark) .input:checked,
:global(.dark) .input[type="checkbox"]:indeterminate {
  border-color: var(--black);
}

:global(.light) .input:checked:hover,
:global(.light) .input[type="checkbox"]:indeterminate:hover,
:global(.dark) .input:checked:hover,
:global(.dark) .input[type="checkbox"]:indeterminate:hover {
  background-color: var(--highlight-4);
}

:global(.light) .input:checked:active,
:global(.light) .input[type="checkbox"]:indeterminate:active,
:global(.dark) .input:checked:active,
:global(.dark) .input[type="checkbox"]:indeterminate:active {
  background-color: var(--highlight-6);
  box-shadow: none;
}

:global(.light) .input:checked ~ .icon,
:global(.light) .input[type="checkbox"]:indeterminate ~ .icon,
:global(.dark) .input:checked ~ .icon,
:global(.dark) .input[type="checkbox"]:indeterminate ~ .icon {
  color: var(--white);
}

/* DISABLED */

:global(.light) .input:disabled:not(.x) {
  box-shadow: none;
  background-color: var(--gray-0);
  border-color: var(--gray-1);
}

:global(.dark) .input:disabled:not(.x) {
  box-shadow: none;
  background-color: var(--gray-6);
  border-color: var(--black);
}

:global(.light) .input:disabled ~ .icon {
  color: var(--gray-3);
}

:global(.dark) .input:disabled ~ .icon {
  color: var(--gray-4);
}

:global(.light) .input:disabled ~ .label {
  color: var(--gray-3);
}

:global(.dark) .input:disabled ~ .label {
  color: var(--gray-4);
}
